.contact-form-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.contact-form {
  background-color: white;
  padding: 2.5rem;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--dark-color);
}

.required {
  color: var(--danger-color);
}

.contact-form input,
.contact-form textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e1e1e1;
  border-radius: 5px;
  font-size: 1rem;
  font-family: inherit;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.contact-form input:focus,
.contact-form textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(74, 107, 255, 0.15);
}

.contact-form textarea {
  resize: vertical;
  min-height: 120px;
}

.form-message {
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-radius: 5px;
  font-weight: 500;
  animation: fadeIn 0.3s ease;
}

.form-message.success {
  background-color: rgba(40, 167, 69, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(40, 167, 69, 0.2);
}

.form-message.error {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--danger-color);
  border: 1px solid rgba(220, 53, 69, 0.2);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media screen and (max-width: 768px) {
  .contact-form {
    padding: 1.5rem;
  }
}
