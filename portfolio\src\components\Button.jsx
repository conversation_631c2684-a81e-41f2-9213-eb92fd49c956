import React from 'react';
import { Link } from 'react-router-dom';
import './Button.css';

function Button({ 
  children, 
  type = 'primary', 
  size = 'medium', 
  to, 
  href, 
  onClick, 
  className = '',
  disabled = false,
  ...props 
}) {
  // Determine the CSS classes to apply
  const buttonClasses = `button ${type} ${size} ${className}`;
  
  // If 'to' prop is provided, render a Link component
  if (to) {
    return (
      <Link to={to} className={buttonClasses} {...props}>
        {children}
      </Link>
    );
  }
  
  // If 'href' prop is provided, render an anchor tag
  if (href) {
    return (
      <a href={href} className={buttonClasses} target="_blank" rel="noopener noreferrer" {...props}>
        {children}
      </a>
    );
  }
  
  // Otherwise, render a button element
  return (
    <button 
      className={buttonClasses} 
      onClick={onClick} 
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  );
}

export default Button;
