.skill-bar {
  margin-bottom: 1.5rem;
  width: 100%;
}

.skill-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.skill-name {
  font-weight: 600;
  color: var(--dark-color);
}

.skill-percentage {
  font-weight: 500;
  color: #666;
}

.skill-progress {
  height: 10px;
  background-color: #f1f1f1;
  border-radius: 5px;
  overflow: hidden;
  position: relative;
}

.skill-progress-bar {
  height: 100%;
  border-radius: 5px;
  transition: width 1.5s cubic-bezier(0.1, 0.5, 0.2, 1);
  width: 0;
}

.skill-progress-bar.animate {
  animation: shimmer 2s infinite linear;
  background-size: 200% 100%;
  background-image: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
