.project-card {
  background-color: var(--card-bg);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid transparent;
}

.project-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  border-color: rgba(108, 143, 255, 0.3);
}

/* Dark theme specific styles */
.dark .project-card {
  border: 1px solid rgba(108, 143, 255, 0.1);
}

.dark .project-card:hover {
  box-shadow: 0 15px 35px rgba(108, 143, 255, 0.15);
  border-color: rgba(108, 143, 255, 0.3);
}

.project-image {
  position: relative;
  overflow: hidden;
  height: 200px;
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.project-card:hover .project-image img {
  transform: scale(1.05);
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-buttons {
  display: flex;
  gap: 10px;
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.3s ease 0.1s;
}

.project-card:hover .project-buttons {
  transform: translateY(0);
  opacity: 1;
}

.project-content {
  padding: 1.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.project-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: var(--text-primary);
}

.project-description {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  flex-grow: 1;
}

.project-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: auto;
}

.project-tag {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.project-tag:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Dark theme specific styles */
.dark .project-tag {
  background-color: rgba(108, 143, 255, 0.1);
  color: var(--text-secondary);
}

.dark .project-tag:hover {
  background-color: var(--primary-color);
  color: white;
}

@media screen and (max-width: 768px) {
  .project-image {
    height: 180px;
  }
}
