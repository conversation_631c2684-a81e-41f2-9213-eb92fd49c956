.about-page {
  width: 100%;
  overflow-x: hidden;
}

/* Page Header */
.page-header {

  padding: 8rem 2rem 4rem;
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;

}

/* Add a subtle pattern to the header */
.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 1%, transparent 5%),
                    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 1%, transparent 5%);
  background-size: 50px 50px;
  opacity: 0.5;
  pointer-events: none;
}

.page-header-content {
  max-width: var(--max-width);
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

/* Profile image styles */
.profile-image-container {
  margin-bottom: 2rem;
  position: relative;
  display: inline-block;
}

.profile-image {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.profile-image:hover {
  transform: scale(1.05);
  box-shadow: 0 15px 40px rgba(255, 255, 255, 0.3);
}

.page-header h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.page-header p {
  font-size: 1.2rem;
  max-width: 700px;
  margin: 0 auto;
  opacity: 0.9;
}

/* Common section styles */
.section-container {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0 2rem;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.section-divider {
  width: 80px;
  height: 4px;
  background-color: var(--primary-color);
  margin: 0 auto;
  border-radius: 2px;
}

/* About Section */
.about-section {
  padding: 6rem 0;

}

.about-content {
  display: flex;
  flex-wrap: wrap;
  gap: 4rem;
  align-items: center;
  justify-content: center;
}

.about-text {
  flex: 1;
  min-width: 300px;
  max-width: 800px;
}

.about-text h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  position: relative;
}

.about-text h2::after {
  content: '';
  position: absolute;
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
  bottom: -10px;
  left: 0;
  border-radius: 2px;
}

.about-text p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

.about-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

/* Experience Section */
.experience-section {
  padding: 6rem 0;
  position: relative;
  overflow: hidden;
}

.experience-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 30%, rgba(108, 143, 255, 0.05) 0%, transparent 15%),
    radial-gradient(circle at 80% 20%, rgba(108, 143, 255, 0.05) 0%, transparent 15%),
    radial-gradient(circle at 40% 70%, rgba(108, 143, 255, 0.05) 0%, transparent 15%),
    radial-gradient(circle at 70% 80%, rgba(108, 143, 255, 0.05) 0%, transparent 15%);
  background-size: 300px 300px;
  z-index: -1;
  opacity: 0.8;
  pointer-events: none;
}

.timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem 0;
}

.timeline::before {
  content: '';
  position: absolute;
  width: 2px;
  background: linear-gradient(
    to bottom,
    rgba(108, 143, 255, 0.1),
    rgba(108, 143, 255, 0.6),
    rgba(108, 143, 255, 0.1)
  );
  top: 0;
  bottom: 0;
  left: 40px;
  margin-left: -1px;
  box-shadow: 0 0 15px rgba(108, 143, 255, 0.3);
}

.timeline-item {
  position: relative;
  margin-bottom: 4rem;
  padding-left: 80px;
  z-index: 1;
}

.timeline-dot {
  position: absolute;
  width: 16px;
  height: 16px;
  background-color: var(--primary-color);
  border-radius: 50%;
  left: 40px;
  top: 15px;
  transform: translateX(-50%);
  z-index: 1;
  box-shadow: 0 0 15px rgba(108, 143, 255, 0.7);
  transition: all 0.3s ease;
}

.timeline-dot::before {
  content: '';
  position: absolute;
  width: 30px;
  height: 30px;
  border: 2px solid rgba(108, 143, 255, 0.3);
  border-radius: 50%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 2s infinite;
}

.timeline-item:hover .timeline-dot {
  transform: translateX(-50%) scale(1.2);
  box-shadow: 0 0 15px rgba(108, 143, 255, 0.8);
}

.timeline-date {
  position: absolute;
  left: 0;
  top: -15px;
  width: 120px;
  text-align: left;
  padding: 5px 10px;
  font-weight: 700;
  color: var(--primary-color);
  background-color: rgba(15, 15, 26, 0.9);
  border-radius: 4px;
  z-index: 2;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3), 0 0 10px rgba(108, 143, 255, 0.2);
  border-left: 3px solid var(--primary-color);
  transform: translateY(-50%);
  font-size: 0.95rem;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.timeline-item:hover .timeline-date {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4), 0 0 15px rgba(108, 143, 255, 0.3);
  color: white;
  background-color: rgba(108, 143, 255, 0.2);
}

.timeline-content {
  background-color: rgba(30, 30, 45, 0.8);
  padding: 1.8rem;
  border-radius: 10px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  border: 1px solid rgba(108, 143, 255, 0.1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
}

.timeline-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 10% 20%, rgba(108, 143, 255, 0.05) 0%, transparent 20%),
    radial-gradient(circle at 80% 70%, rgba(108, 143, 255, 0.05) 0%, transparent 20%),
    linear-gradient(to bottom right, rgba(30, 30, 60, 0.4) 0%, rgba(15, 15, 30, 0.4) 100%);
  background-size: 150px 150px, 150px 150px, 100% 100%;
  z-index: -1;
  opacity: 0.8;
  pointer-events: none;
}

.timeline-item:hover .timeline-content {
  transform: translateY(-5px);
  border-color: var(--primary-color);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4), 0 0 20px rgba(108, 143, 255, 0.2);
}

/* Dark theme glow effect */
.dark .timeline-item:hover .timeline-content {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4), 0 0 20px rgba(108, 143, 255, 0.2);
  border-color: var(--primary-color);
}

.timeline-content h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: white;
  letter-spacing: 0.5px;
  position: relative;
  padding-bottom: 8px;
}

.timeline-content h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

.timeline-content h4 {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--primary-color);
  margin-bottom: 1.2rem;
  opacity: 0.9;
}

.timeline-content p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1.2rem;
  line-height: 1.6;
  font-size: 1rem;
}

.timeline-content ul {
  padding-left: 1.5rem;
  color: rgba(255, 255, 255, 0.7);
  list-style-type: none;
}

.timeline-content ul li {
  margin-bottom: 0.7rem;
  position: relative;
  padding-left: 1rem;
}

.timeline-content ul li::before {
  content: '•';
  color: var(--primary-color);
  position: absolute;
  left: -0.5rem;
  font-size: 1.2rem;
}

/* Education Section */
.education-section {
  padding: 6rem 0;

}

.education-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
}

.education-card {
  flex: 1;
  min-width: 300px;
  max-width: 400px;
  background-color: var(--card-bg);
  padding: 2rem;
  border-radius: 10px;
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.education-card:hover {
  transform: translateY(-10px);
  border-color: var(--primary-color);
}

/* Dark theme glow effect */
.dark .education-card:hover {
  box-shadow: 0 15px 35px rgba(108, 143, 255, 0.15);
}

.education-icon {
  width: 60px;
  height: 60px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1.5rem;
  box-shadow: 0 10px 20px rgba(108, 143, 255, 0.2);
  transition: all 0.3s ease;
}

.education-card:hover .education-icon {
  transform: scale(1.1);
  box-shadow: 0 15px 30px rgba(108, 143, 255, 0.3);
}

.education-icon i {
  font-size: 1.5rem;
}

.education-card h3 {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.education-card h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.education-date {
  font-size: 0.9rem;
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 1rem;
}

.education-card p {
  color: var(--text-secondary);
}

/* Contact CTA Section */
.contact-cta-section {
  padding: 6rem 0;

  color: white;
  position: relative;
  overflow: hidden;
}

/* Add a subtle pattern to the CTA section */
.contact-cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 1%, transparent 5%),
                    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 1%, transparent 5%);
  background-size: 50px 50px;
  opacity: 0.5;
  pointer-events: none;
}

.contact-cta-content {
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.contact-cta-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.contact-cta-content p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.contact-cta-content .button.primary {
  background-color: white;
  color: var(--primary-color);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.contact-cta-content .button.primary:hover {
  background-color: rgba(255, 255, 255, 0.9);
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

/* Responsive Styles */
@media screen and (max-width: 992px) {
  .about-content {
    flex-direction: column;
    align-items: center;
  }

  .about-buttons {
    justify-content: center;
  }

  .section-title {
    font-size: 2rem;
  }

  .timeline::before {
    left: 25px;
  }

  .timeline-item {
    padding-left: 50px;
  }

  .timeline-dot {
    left: 25px;
  }

  .timeline-date {
    width: auto;
    font-size: 0.85rem;
    left: 50px;
    top: -5px;
    transform: none;
  }
}

@media screen and (max-width: 768px) {
  .page-header h1 {
    font-size: 2.5rem;
  }

  .section-container {
    padding: 0 1.5rem;
  }

  .about-buttons {
    flex-direction: column;
    gap: 1rem;
  }

  .education-card {
    min-width: 100%;
  }

  .profile-image {
    width: 120px;
    height: 120px;
  }
}

@media screen and (max-width: 480px) {
  .page-header h1 {
    font-size: 2rem;
  }

  .page-header p {
    font-size: 1rem;
  }

  .profile-image {
    width: 100px;
    height: 100px;
  }

  .about-text h2 {
    font-size: 1.5rem;
  }

  .about-text p {
    font-size: 1rem;
  }

  .timeline-content {
    padding: 1.2rem;
  }

  .timeline-content h3 {
    font-size: 1.2rem;
    padding-bottom: 6px;
  }

  .timeline-content h3::after {
    width: 30px;
    height: 2px;
  }

  .timeline-content h4 {
    font-size: 0.95rem;
  }

  .timeline-content p,
  .timeline-content ul li {
    font-size: 0.9rem;
  }

  .education-icon {
    width: 50px;
    height: 50px;
  }

  .education-icon i {
    font-size: 1.2rem;
  }

  .contact-cta-content h2 {
    font-size: 1.8rem;
  }

  .contact-cta-content p {
    font-size: 1rem;
  }
}

/* Animations */
@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(108, 143, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(108, 143, 255, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(108, 143, 255, 0.5);
  }
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.2;
  }
  100% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.8;
  }
}

.dark .education-card:hover,
.dark .timeline-content:hover {
  animation: glow 2s infinite;
}
