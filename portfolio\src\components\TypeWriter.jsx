import React, { useState, useEffect, useRef } from 'react';
import './TypeWriter.css';

function TypeWriter({ 
  texts = [], 
  typingSpeed = 100, 
  deletingSpeed = 50, 
  delayBetweenTexts = 2000 
}) {
  const [currentTextIndex, setCurrentTextIndex] = useState(0);
  const [currentText, setCurrentText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const timeoutRef = useRef(null);
  
  useEffect(() => {
    if (texts.length === 0) return;
    
    const handleTyping = () => {
      const currentFullText = texts[currentTextIndex];
      
      if (isPaused) {
        timeoutRef.current = setTimeout(() => {
          setIsPaused(false);
          setIsDeleting(true);
        }, delayBetweenTexts);
        return;
      }
      
      if (isDeleting) {
        // Deleting text
        setCurrentText(currentFullText.substring(0, currentText.length - 1));
        
        if (currentText.length === 0) {
          setIsDeleting(false);
          setCurrentTextIndex((currentTextIndex + 1) % texts.length);
        }
      } else {
        // Typing text
        setCurrentText(currentFullText.substring(0, currentText.length + 1));
        
        if (currentText.length === currentFullText.length) {
          setIsPaused(true);
        }
      }
    };
    
    const typingDelay = isDeleting ? deletingSpeed : typingSpeed;
    
    timeoutRef.current = setTimeout(handleTyping, typingDelay);
    
    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
  }, [currentText, currentTextIndex, isDeleting, isPaused, texts, typingSpeed, deletingSpeed, delayBetweenTexts]);
  
  return (
    <span className="typewriter">
      {currentText}
      <span className="cursor"></span>
    </span>
  );
}

export default TypeWriter;
