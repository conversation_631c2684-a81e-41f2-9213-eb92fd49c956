import React, { useState, useEffect, useRef } from 'react';
import './SkillBar.css';

function SkillBar({ name, percentage, color }) {
  const [isVisible, setIsVisible] = useState(false);
  const skillRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.unobserve(entry.target);
        }
      },
      { threshold: 0.2 }
    );

    if (skillRef.current) {
      observer.observe(skillRef.current);
    }

    return () => {
      if (skillRef.current) {
        observer.unobserve(skillRef.current);
      }
    };
  }, []);

  return (
    <div className="skill-bar" ref={skillRef}>
      <div className="skill-info">
        <span className="skill-name">{name}</span>
        <span className="skill-percentage">{percentage}%</span>
      </div>
      <div className="skill-progress">
        <div 
          className={`skill-progress-bar ${isVisible ? 'animate' : ''}`} 
          style={{ 
            width: isVisible ? `${percentage}%` : '0%',
            backgroundColor: color || 'var(--primary-color)'
          }}
        ></div>
      </div>
    </div>
  );
}

export default SkillBar;
