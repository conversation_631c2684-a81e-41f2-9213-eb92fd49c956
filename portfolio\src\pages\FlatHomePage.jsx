import React from 'react';
import { Link } from 'react-router-dom';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import Button from '../components/Button';
import TypeWriter from '../components/TypeWriter';
import ScrollAnimation from '../components/ScrollAnimation';
import { useTheme } from '../context/ThemeContext';
import './FlatHomePage.css';
import image from '../assets/image.png';

function FlatHomePage() {
  const { theme } = useTheme();

  // TypeWriter texts
  const typewriterTexts = [
    "Software Developer",
    "Frontend Developer",
    "React Developer",
    "Problem Solver"
  ];

  return (
    <div className={`home-page ${theme}`}>
      <Navbar />

      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-content-wrapper">
          <ScrollAnimation animation="fade-up">
            <div className="profile-image-container">
              {/* Replace with your image */}
              <img src={image} alt="Divy Patel" className="profile-image" />
            </div>
          </ScrollAnimation>

          <ScrollAnimation animation="fade-up" delay={0.2}>
            <div className="hero-content">
              <h1 className="hero-title">
                Hi, I'm <span className="highlight">Divy Patel</span>
              </h1>
              <h2 className="hero-subtitle">
                <TypeWriter texts={typewriterTexts} typingSpeed={100} deletingSpeed={50} />
              </h2>
              <p className="hero-description">
                I'm a passionate Computer Science student and Software Developer Intern at Fab AF.
                I specialize in frontend development using HTML, CSS, JavaScript, and React to build
                responsive and user-friendly web interfaces.
              </p>
              <div className="hero-buttons">
                <Button to="/projects" type="primary" size="large">
                  View My Work
                </Button>
                <Button to="/contact" type="secondary" size="large">
                  Contact Me
                </Button>
              </div>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* About Preview Section */}
      <section className="about-preview-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="section-header">
              <h2 className="section-title">About Me</h2>
              <div className="section-divider"></div>
            </div>
          </ScrollAnimation>

          <div className="about-preview-content">
            <ScrollAnimation animation="fade-right" delay={0.2}>
              <div className="about-preview-text">
                <p>
                  I'm a passionate Computer Science student at Indus University with a CGPA of 8.4.
                  Currently working as a Software Developer Intern at Fab AF, where I focus on frontend
                  development using HTML, CSS, JavaScript, and React to build responsive web interfaces.
                </p>
                <p>
                  My journey in software development includes building comprehensive platforms like a
                  Farmer's Market Platform with Django and an Auto Auction System, along with various
                  e-commerce solutions using different technology stacks.
                </p>
                <Button to="/about" type="text">
                  Learn more about me <i className="fas fa-arrow-right"></i>
                </Button>
              </div>
            </ScrollAnimation>
            <ScrollAnimation animation="fade-left" delay={0.4}>
              <div className="about-preview-stats">
                <div className="stat-item">
                  <span className="stat-number">8.4</span>
                  <span className="stat-label">CGPA</span>
                </div>
                <div className="stat-item">
                  <span className="stat-number">3+</span>
                  <span className="stat-label">Major Projects</span>
                </div>
                <div className="stat-item">
                  <span className="stat-number">3+</span>
                  <span className="stat-label">Certifications</span>
                </div>
              </div>
            </ScrollAnimation>
          </div>
        </div>
      </section>

      {/* Skills Preview Section */}
      <section className="skills-preview-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="section-header">
              <h2 className="section-title">My Skills</h2>
              <div className="section-divider"></div>
            </div>
          </ScrollAnimation>

          <div className="skills-preview-content">
            <ScrollAnimation animation="stagger" delay={0.2}>
              <div className="skills-grid">
                <div className="skill-category">
                  <h3>Frontend</h3>
                  <ul>
                    <li><i className="fab fa-html5"></i> HTML5</li>
                    <li><i className="fab fa-css3-alt"></i> CSS3</li>
                    <li><i className="fab fa-js"></i> JavaScript</li>
                    <li><i className="fab fa-react"></i> React</li>
                  </ul>
                </div>
                <div className="skill-category">
                  <h3>Backend</h3>
                  <ul>
                    <li><i className="fab fa-python"></i> Python</li>
                    <li><i className="fas fa-database"></i> Django</li>
                    <li><i className="fas fa-database"></i> MySQL</li>
                    <li><i className="fas fa-database"></i> SQLite</li>
                  </ul>
                </div>
                <div className="skill-category">
                  <h3>Tools</h3>
                  <ul>
                    <li><i className="fab fa-git-alt"></i> Git</li>
                    <li><i className="fab fa-github"></i> GitHub</li>
                    <li><i className="fas fa-code"></i> VS Code</li>
                    <li><i className="fas fa-code"></i> Eclipse</li>
                  </ul>
                </div>
              </div>
            </ScrollAnimation>
            <ScrollAnimation animation="fade-up" delay={0.6}>
              <div className="skills-cta">
                <Button to="/skills" type="primary">
                  View All Skills
                </Button>
              </div>
            </ScrollAnimation>
          </div>
        </div>
      </section>

      {/* Projects Preview Section */}
      <section className="projects-preview-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="section-header">
              <h2 className="section-title">Featured Projects</h2>
              <div className="section-divider"></div>
            </div>
          </ScrollAnimation>

          <ScrollAnimation animation="stagger" delay={0.2}>
            <div className="projects-preview-grid">
              {/* Project 1 */}
              <div className="project-preview-card">
                <div className="project-preview-image">
                  <img src="https://via.placeholder.com/600x400" alt="Farmer's Market Platform" />
                </div>
                <div className="project-preview-content">
                  <h3>Farmer's Market Platform</h3>
                  <p>A comprehensive platform connecting local farmers directly with consumers, featuring dual user roles and Google Maps API integration.</p>
                  <div className="project-preview-tags">
                    <span>Django</span>
                    <span>Bootstrap</span>
                    <span>JavaScript</span>
                    <span>SQLite</span>
                  </div>
                </div>
              </div>

              {/* Project 2 */}
              <div className="project-preview-card">
                <div className="project-preview-image">
                  <img src="https://via.placeholder.com/600x400" alt="Auto Auction System" />
                </div>
                <div className="project-preview-content">
                  <h3>Auto Auction System</h3>
                  <p>A web-based auto auction platform with real-time bidding functionality, user authentication, and automated winner determination.</p>
                  <div className="project-preview-tags">
                    <span>Django</span>
                    <span>HTML/CSS</span>
                    <span>JavaScript</span>
                    <span>SQLite</span>
                  </div>
                </div>
              </div>
            </div>
          </ScrollAnimation>

          <ScrollAnimation animation="fade-up" delay={0.6}>
            <div className="projects-cta">
              <Button to="/projects" type="secondary">
                View All Projects
              </Button>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Contact CTA Section */}
      <section className="contact-cta-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="contact-cta-content">
              <h2>Let's Work Together</h2>
              <p>Have a project in mind? I'd love to hear about it and see how I can help.</p>
              <Button to="/contact" type="primary" size="large">
                Get In Touch
              </Button>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      <Footer />
    </div>
  );
}

export default FlatHomePage;
