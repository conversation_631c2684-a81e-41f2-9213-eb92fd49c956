import React from 'react';
import { Link } from 'react-router-dom';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import Button from '../components/Button';
import TypeWriter from '../components/TypeWriter';
import ScrollAnimation from '../components/ScrollAnimation';
import { useTheme } from '../context/ThemeContext';
import './FlatHomePage.css';

function FlatHomePage() {
  const { theme } = useTheme();

  // TypeWriter texts
  const typewriterTexts = [
    "Software Developer",
    "Frontend Specialist",
    "UI/UX Enthusiast",
    "Problem Solver"
  ];

  return (
    <div className={`home-page ${theme}`}>
      <Navbar />

      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-content-wrapper">
          <ScrollAnimation animation="fade-up">
            <div className="profile-image-container">
              {/* Replace with your image */}
              <img src="https://via.placeholder.com/300" alt="John Doe" className="profile-image" />
            </div>
          </ScrollAnimation>

          <ScrollAnimation animation="fade-up" delay={0.2}>
            <div className="hero-content">
              <h1 className="hero-title">
                Hi, I'm <span className="highlight">John Doe</span>
              </h1>
              <h2 className="hero-subtitle">
                <TypeWriter texts={typewriterTexts} typingSpeed={100} deletingSpeed={50} />
              </h2>
              <p className="hero-description">
                I build exceptional digital experiences with clean, efficient code.
                Specializing in full-stack development with a passion for creating
                intuitive, user-friendly applications.
              </p>
              <div className="hero-buttons">
                <Button to="/projects" type="primary" size="large">
                  View My Work
                </Button>
                <Button to="/contact" type="secondary" size="large">
                  Contact Me
                </Button>
              </div>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* About Preview Section */}
      <section className="about-preview-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="section-header">
              <h2 className="section-title">About Me</h2>
              <div className="section-divider"></div>
            </div>
          </ScrollAnimation>

          <div className="about-preview-content">
            <ScrollAnimation animation="fade-right" delay={0.2}>
              <div className="about-preview-text">
                <p>
                  I'm a passionate software developer with expertise in building web applications
                  using modern technologies. With a strong foundation in both frontend and backend
                  development, I create seamless, responsive, and user-friendly experiences.
                </p>
                <p>
                  My journey in software development began 5 years ago, and since then, I've
                  worked on various projects ranging from small business websites to complex
                  enterprise applications.
                </p>
                <Button to="/about" type="text">
                  Learn more about me <i className="fas fa-arrow-right"></i>
                </Button>
              </div>
            </ScrollAnimation>
            <ScrollAnimation animation="fade-left" delay={0.4}>
              <div className="about-preview-stats">
                <div className="stat-item">
                  <span className="stat-number">5+</span>
                  <span className="stat-label">Years Experience</span>
                </div>
                <div className="stat-item">
                  <span className="stat-number">50+</span>
                  <span className="stat-label">Projects Completed</span>
                </div>
                <div className="stat-item">
                  <span className="stat-number">20+</span>
                  <span className="stat-label">Happy Clients</span>
                </div>
              </div>
            </ScrollAnimation>
          </div>
        </div>
      </section>

      {/* Skills Preview Section */}
      <section className="skills-preview-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="section-header">
              <h2 className="section-title">My Skills</h2>
              <div className="section-divider"></div>
            </div>
          </ScrollAnimation>

          <div className="skills-preview-content">
            <ScrollAnimation animation="stagger" delay={0.2}>
              <div className="skills-grid">
                <div className="skill-category">
                  <h3>Frontend</h3>
                  <ul>
                    <li><i className="fab fa-html5"></i> HTML5</li>
                    <li><i className="fab fa-css3-alt"></i> CSS3</li>
                    <li><i className="fab fa-js"></i> JavaScript</li>
                    <li><i className="fab fa-react"></i> React</li>
                  </ul>
                </div>
                <div className="skill-category">
                  <h3>Backend</h3>
                  <ul>
                    <li><i className="fab fa-node-js"></i> Node.js</li>
                    <li><i className="fas fa-database"></i> MongoDB</li>
                    <li><i className="fas fa-server"></i> Express</li>
                    <li><i className="fas fa-fire"></i> Firebase</li>
                  </ul>
                </div>
                <div className="skill-category">
                  <h3>Tools</h3>
                  <ul>
                    <li><i className="fab fa-git-alt"></i> Git</li>
                    <li><i className="fab fa-github"></i> GitHub</li>
                    <li><i className="fab fa-npm"></i> npm</li>
                    <li><i className="fas fa-terminal"></i> Command Line</li>
                  </ul>
                </div>
              </div>
            </ScrollAnimation>
            <ScrollAnimation animation="fade-up" delay={0.6}>
              <div className="skills-cta">
                <Button to="/skills" type="primary">
                  View All Skills
                </Button>
              </div>
            </ScrollAnimation>
          </div>
        </div>
      </section>

      {/* Projects Preview Section */}
      <section className="projects-preview-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="section-header">
              <h2 className="section-title">Featured Projects</h2>
              <div className="section-divider"></div>
            </div>
          </ScrollAnimation>

          <ScrollAnimation animation="stagger" delay={0.2}>
            <div className="projects-preview-grid">
              {/* Project 1 */}
              <div className="project-preview-card">
                <div className="project-preview-image">
                  <img src="https://via.placeholder.com/600x400" alt="Project 1" />
                </div>
                <div className="project-preview-content">
                  <h3>E-Commerce Platform</h3>
                  <p>A full-featured online store with product management, cart functionality, and payment processing.</p>
                  <div className="project-preview-tags">
                    <span>React</span>
                    <span>Node.js</span>
                    <span>MongoDB</span>
                  </div>
                </div>
              </div>

              {/* Project 2 */}
              <div className="project-preview-card">
                <div className="project-preview-image">
                  <img src="https://via.placeholder.com/600x400" alt="Project 2" />
                </div>
                <div className="project-preview-content">
                  <h3>Task Management App</h3>
                  <p>A productivity application for managing tasks, projects, and team collaboration.</p>
                  <div className="project-preview-tags">
                    <span>React</span>
                    <span>Firebase</span>
                    <span>Material UI</span>
                  </div>
                </div>
              </div>
            </div>
          </ScrollAnimation>

          <ScrollAnimation animation="fade-up" delay={0.6}>
            <div className="projects-cta">
              <Button to="/projects" type="secondary">
                View All Projects
              </Button>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Contact CTA Section */}
      <section className="contact-cta-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="contact-cta-content">
              <h2>Let's Work Together</h2>
              <p>Have a project in mind? I'd love to hear about it and see how I can help.</p>
              <Button to="/contact" type="primary" size="large">
                Get In Touch
              </Button>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      <Footer />
    </div>
  );
}

export default FlatHomePage;
