:root {
  /* Common variables */
  --max-width: 1200px;
  --border-radius: 10px;
  --transition-speed: 0.3s;

  /* Light theme (default) */
  --primary-color: #4a6bff;
  --secondary-color: #ff6b6b;
  --dark-color: #333;
  --light-color: #f4f4f4;
  --success-color: #28a745;
  --danger-color: #dc3545;

  /* Light theme specific */
  --bg-primary: #ffffff;
  --bg-secondary: #f9f9f9;
  --bg-tertiary: #f1f1f1;
  --text-primary: #333333;
  --text-secondary: #555555;
  --text-tertiary: #777777;
  --border-color: #e1e1e1;
  --card-bg: #ffffff;
  --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  --navbar-bg: rgba(255, 255, 255, 0.95);
  --navbar-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Dark theme variables */
[data-theme="dark"] {
  --primary-color: #6c8fff;
  --secondary-color: #ff7b7b;
  --dark-color: #f4f4f4;
  --light-color: #333;
  --success-color: #2ebd4e;
  --danger-color: #e74c3c;

  /* Dark theme specific */
  --bg-primary: #0f0f1a;
  --bg-secondary: #121824;
  --bg-tertiary: #1a2030;
  --text-primary: #f4f4f4;
  --text-secondary: #cccccc;
  --text-tertiary: #999999;
  --border-color: #3d3d3d;
  --card-bg: #1e1e1e;
  --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  --navbar-bg: rgba(18, 18, 18, 0.95);
  --navbar-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  transition: background-color var(--transition-speed) ease,
              color var(--transition-speed) ease,
              border-color var(--transition-speed) ease;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: #0f0f1a; /* Fixed dark space background */
  background-image:
    radial-gradient(circle at 25% 25%, rgba(30, 40, 70, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(20, 30, 60, 0.2) 0%, transparent 50%);
  background-attachment: fixed;
  background-size: 100% 100%;
  overflow-x: hidden;
}

a {
  text-decoration: none;
  color: var(--primary-color);
}

a:hover {
  text-decoration: underline;
}

/* For better accessibility */
button:focus, a:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* For better mobile experience */
@media (max-width: 768px) {
  h1 {
    font-size: 1.8rem;
  }

  h2 {
    font-size: 1.5rem;
  }
}
