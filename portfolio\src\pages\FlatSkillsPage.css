.skills-page {
  width: 100%;
  overflow-x: hidden;
}

/* Page Header */
.page-header {
  
  padding: 8rem 2rem 4rem;
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
  

}

/* Add a subtle pattern to the header */
.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 1%, transparent 5%),
                    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 1%, transparent 5%);
  background-size: 50px 50px;
  opacity: 0.5;
  pointer-events: none;
}

.page-header-content {
  max-width: var(--max-width);
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

/* Profile image styles */
.profile-image-container {
  margin-bottom: 2rem;
  position: relative;
  display: inline-block;
}

.profile-image {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.profile-image:hover {
  transform: scale(1.05);
  box-shadow: 0 15px 40px rgba(255, 255, 255, 0.3);
}

.page-header h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.page-header p {
  font-size: 1.2rem;
  max-width: 700px;
  margin: 0 auto;
  opacity: 0.9;
}

/* Common section styles */
.section-container {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0 2rem;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.section-divider {
  width: 80px;
  height: 4px;
  background-color: var(--primary-color);
  margin: 0 auto;
  border-radius: 2px;
}

/* Skills Overview Section */
.skills-overview-section {
  padding: 6rem 0;
  
}

.skills-overview-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.skills-overview-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.skills-overview-content p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

/* Skills Category Section */
.skills-category-section {
  padding: 6rem 0;
 
}



.skills-category-header {
  text-align: center;
  margin-bottom: 3rem;
}

.skills-category-icon {
  width: 80px;
  height: 80px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 1.5rem;
  box-shadow: 0 10px 20px rgba(108, 143, 255, 0.2);
  transition: all 0.3s ease;
}

.skills-category-icon:hover {
  transform: scale(1.05);
  box-shadow: 0 15px 30px rgba(108, 143, 255, 0.3);
}

.skills-category-icon i {
  font-size: 2rem;
}

.skills-category-header h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.skills-category-header p {
  font-size: 1.1rem;
  color: var(--text-secondary);
  max-width: 700px;
  margin: 0 auto;
}

.skills-bars {
  max-width: 800px;
  margin: 0 auto;
}

/* Soft Skills Section */
.soft-skills-section {
  padding: 6rem 0;
 
}

.soft-skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.soft-skill-card {
  background-color: var(--card-bg);
  padding: 2rem;
  border-radius: 10px;
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
  text-align: center;
  border: 1px solid transparent;
}

.soft-skill-card:hover {
  transform: translateY(-10px);
  border-color: var(--primary-color);
}

/* Dark theme glow effect */
.dark .soft-skill-card:hover {
  box-shadow: 0 15px 35px rgba(108, 143, 255, 0.15);
}

.soft-skill-icon {
  width: 70px;
  height: 70px;
  background-color: rgba(108, 143, 255, 0.1);
  color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 1.5rem;
  transition: all 0.3s ease;
}

.soft-skill-card:hover .soft-skill-icon {
  background-color: var(--primary-color);
  color: white;
  transform: scale(1.1);
}

.soft-skill-icon i {
  font-size: 1.8rem;
}

.soft-skill-card h3 {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.soft-skill-card p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Learning Section */
.learning-section {
  padding: 6rem 0;
  background-color: var(--primary-color);
  color: white;
  position: relative;
  overflow: hidden;
}

/* Add a subtle pattern to the learning section */
.learning-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 1%, transparent 5%),
                    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 1%, transparent 5%);
  background-size: 50px 50px;
  opacity: 0.5;
  pointer-events: none;
}

.learning-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 1;
}

.learning-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.learning-content p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.learning-tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
}

.learning-tags span {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.learning-tags span:hover {
  background-color: white;
  color: var(--primary-color);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(255, 255, 255, 0.3);
}

/* Animation for dark mode */
@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(108, 143, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(108, 143, 255, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(108, 143, 255, 0.5);
  }
}

.dark .soft-skill-card:hover {
  animation: glow 2s infinite;
}

/* Responsive Styles */
@media screen and (max-width: 992px) {
  .page-header h1 {
    font-size: 2.5rem;
  }

  .skills-overview-content h2,
  .skills-category-header h2,
  .section-title,
  .learning-content h2 {
    font-size: 2rem;
  }
}

@media screen and (max-width: 768px) {
  .section-container {
    padding: 0 1.5rem;
  }

  .soft-skills-grid {
    grid-template-columns: 1fr;
  }

  .learning-tags span {
    font-size: 0.8rem;
  }

  .profile-image {
    width: 120px;
    height: 120px;
  }
}

@media screen and (max-width: 480px) {
  .page-header h1 {
    font-size: 2rem;
  }

  .page-header p {
    font-size: 1rem;
  }

  .profile-image {
    width: 100px;
    height: 100px;
  }

  .skills-category-icon {
    width: 60px;
    height: 60px;
  }

  .skills-category-icon i {
    font-size: 1.5rem;
  }
}
