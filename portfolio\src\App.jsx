import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import ScrollToTop from './components/ScrollToTop';
import ParticlesBackground from './components/ParticlesBackground';
import { ThemeProvider } from './context/ThemeContext';
import FlatHomePage from './pages/FlatHomePage';
import FlatAboutPage from './pages/FlatAboutPage';
import FlatSkillsPage from './pages/FlatSkillsPage';
import FlatProjectsPage from './pages/FlatProjectsPage';

import FlatNotFoundPage from './pages/FlatNotFoundPage';

function App() {
  return (
    <ThemeProvider>
      <BrowserRouter>
        <ScrollToTop />
        <ParticlesBackground />
        <Routes>
          <Route path="/" element={<FlatHomePage />} />
          <Route path="/about" element={<FlatAboutPage />} />
          <Route path="/skills" element={<FlatSkillsPage />} />
          <Route path="/projects" element={<FlatProjectsPage />} />
         
          <Route path="*" element={<FlatNotFoundPage />} />
        </Routes>
      </BrowserRouter>
    </ThemeProvider>
  );
}

export default App;
