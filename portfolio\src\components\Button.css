.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  border: none;
  outline: none;
  font-family: inherit;
  white-space: nowrap;
}

/* Button types */
.button.primary {
  background-color: var(--primary-color);
  color: white;
  box-shadow: 0 4px 14px rgba(74, 107, 255, 0.25);
}

.button.primary:hover {
  background-color: #3a5bef;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(74, 107, 255, 0.35);
  text-decoration: none;
}

.button.secondary {
  background-color: white;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.button.secondary:hover {
  background-color: rgba(74, 107, 255, 0.05);
  transform: translateY(-2px);
  text-decoration: none;
}

.button.outline {
  background-color: transparent;
  color: var(--dark-color);
  border: 2px solid rgba(0, 0, 0, 0.1);
}

.button.outline:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-2px);
  text-decoration: none;
}

.button.text {
  background-color: transparent;
  color: var(--primary-color);
  padding: 0;
  position: relative;
}

.button.text::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

.button.text:hover {
  text-decoration: none;
}

.button.text:hover::after {
  width: 100%;
}

/* Button sizes */
.button.small {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.button.medium {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

.button.large {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

/* Disabled state */
.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Button with icon */
.button i, .button svg {
  margin-right: 0.5rem;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .button.large {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
}
