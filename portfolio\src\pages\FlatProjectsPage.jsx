import React, { useState } from 'react';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import ProjectCard from '../components/ProjectCard';
import ScrollAnimation from '../components/ScrollAnimation';
import { useTheme } from '../context/ThemeContext';
import './FlatProjectsPage.css';

function FlatProjectsPage() {
  // Get theme from context
  const { theme } = useTheme();

  // Project categories
  const categories = ['All', 'Web App', 'Mobile', 'UI/UX', 'Backend'];

  // State for active category
  const [activeCategory, setActiveCategory] = useState('All');

  // Projects data
  const projects = [
    {
      id: 1,
      title: 'E-Commerce Platform',
      description: 'A full-featured online store with product management, cart functionality, and payment processing.',
      image: 'https://via.placeholder.com/600x400',
      tags: ['React', 'Node.js', 'MongoDB', 'Express'],
      category: 'Web App',
      demoLink: 'https://example.com',
      codeLink: 'https://github.com',
    },
    {
      id: 2,
      title: 'Task Management App',
      description: 'A productivity application for managing tasks, projects, and team collaboration.',
      image: 'https://via.placeholder.com/600x400',
      tags: ['React', 'Firebase', 'Material UI'],
      category: 'Web App',
      demoLink: 'https://example.com',
      codeLink: 'https://github.com',
    },
    {
      id: 3,
      title: 'Weather Dashboard',
      description: 'A weather application that displays current and forecasted weather data for any location.',
      image: 'https://via.placeholder.com/600x400',
      tags: ['JavaScript', 'API Integration', 'CSS3'],
      category: 'Web App',
      demoLink: 'https://example.com',
      codeLink: 'https://github.com',
    },
    {
      id: 4,
      title: 'Social Media Mobile App',
      description: 'A cross-platform mobile application for social networking and content sharing.',
      image: 'https://via.placeholder.com/600x400',
      tags: ['React Native', 'Firebase', 'Redux'],
      category: 'Mobile',
      demoLink: 'https://example.com',
      codeLink: 'https://github.com',
    },
    {
      id: 5,
      title: 'Portfolio Website Design',
      description: 'A modern and responsive portfolio website design for creative professionals.',
      image: 'https://via.placeholder.com/600x400',
      tags: ['Figma', 'UI/UX', 'Prototyping'],
      category: 'UI/UX',
      demoLink: 'https://example.com',
      codeLink: 'https://github.com',
    },
    {
      id: 6,
      title: 'RESTful API Service',
      description: 'A robust API service for data management and integration with frontend applications.',
      image: 'https://via.placeholder.com/600x400',
      tags: ['Node.js', 'Express', 'MongoDB', 'JWT'],
      category: 'Backend',
      demoLink: 'https://example.com',
      codeLink: 'https://github.com',
    },
  ];

  // Filter projects based on active category
  const filteredProjects = activeCategory === 'All'
    ? projects
    : projects.filter(project => project.category === activeCategory);

  return (
    <div className={`projects-page ${theme}`}>
      <Navbar />

      {/* Page Header */}
      <header className="page-header">
        <div className="page-header-content">
          <ScrollAnimation animation="fade-up">
            <h1>My Projects</h1>
            <p>A showcase of my work, personal projects, and contributions.</p>
          </ScrollAnimation>
        </div>
      </header>

      {/* Projects Section */}
      <section className="projects-section">
        <div className="section-container">
          {/* Category Filter */}
          <ScrollAnimation animation="fade-up">
            <div className="category-filter">
              {categories.map((category, index) => (
                <button
                  key={index}
                  className={`category-button ${activeCategory === category ? 'active' : ''}`}
                  onClick={() => setActiveCategory(category)}
                >
                  {category}
                </button>
              ))}
            </div>
          </ScrollAnimation>

          {/* Projects Grid */}
          <div className="projects-grid">
            {filteredProjects.map((project, index) => (
              <ScrollAnimation key={project.id} animation="fade-up" delay={index * 0.1}>
                <ProjectCard
                  title={project.title}
                  description={project.description}
                  image={project.image}
                  tags={project.tags}
                  demoLink={project.demoLink}
                  codeLink={project.codeLink}
                />
              </ScrollAnimation>
            ))}
          </div>

          {/* Empty State */}
          {filteredProjects.length === 0 && (
            <ScrollAnimation animation="fade-up">
              <div className="empty-state">
                <i className="fas fa-folder-open"></i>
                <h3>No projects found</h3>
                <p>There are no projects in this category yet.</p>
              </div>
            </ScrollAnimation>
          )}
        </div>
      </section>

      {/* Project Process Section */}
      <section className="project-process-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="section-header">
              <h2 className="section-title">My Development Process</h2>
              <div className="section-divider"></div>
            </div>
          </ScrollAnimation>

          <div className="process-steps">
            <ScrollAnimation animation="fade-up" delay={0.1}>
              <div className="process-step">
                <div className="process-step-number">1</div>
                <div className="process-step-content">
                  <h3>Research & Planning</h3>
                  <p>
                    I begin each project with thorough research and planning to understand the requirements,
                    target audience, and project goals. This phase includes market research, competitor analysis,
                    and defining the project scope.
                  </p>
                </div>
              </div>
            </ScrollAnimation>

            <ScrollAnimation animation="fade-up" delay={0.2}>
              <div className="process-step">
                <div className="process-step-number">2</div>
                <div className="process-step-content">
                  <h3>Design & Prototyping</h3>
                  <p>
                    Next, I create wireframes and prototypes to visualize the user interface and experience.
                    This iterative process helps refine the design before moving to development, ensuring
                    the final product meets all requirements.
                  </p>
                </div>
              </div>
            </ScrollAnimation>

            <ScrollAnimation animation="fade-up" delay={0.3}>
              <div className="process-step">
                <div className="process-step-number">3</div>
                <div className="process-step-content">
                  <h3>Development</h3>
                  <p>
                    During the development phase, I write clean, efficient, and maintainable code following
                    best practices and industry standards. I use modern frameworks and tools to build
                    robust applications with a focus on performance and scalability.
                  </p>
                </div>
              </div>
            </ScrollAnimation>

            <ScrollAnimation animation="fade-up" delay={0.4}>
              <div className="process-step">
                <div className="process-step-number">4</div>
                <div className="process-step-content">
                  <h3>Testing & Deployment</h3>
                  <p>
                    Before deployment, I thoroughly test the application to ensure it works as expected
                    across different devices and browsers. After fixing any issues, I deploy the application
                    and provide documentation for future maintenance.
                  </p>
                </div>
              </div>
            </ScrollAnimation>
          </div>
        </div>
      </section>

      {/* Collaboration CTA Section */}
      <section className="collaboration-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="collaboration-content">
              <h2>Interested in Collaborating?</h2>
              <p>
                I'm always open to discussing new projects, creative ideas, or opportunities to be part of your vision.
              </p>
              <a href="mailto:<EMAIL>" className="contact-button">
                <i className="fas fa-envelope"></i> Get In Touch
              </a>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      <Footer />
    </div>
  );
}

export default FlatProjectsPage;
