.projects-page {
  width: 100%;
  overflow-x: hidden;
}

/* Page Header */
.page-header {

  padding: 8rem 2rem 4rem;
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
}

/* Add a subtle pattern to the header */
.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 1%, transparent 5%),
                    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 1%, transparent 5%);
  background-size: 50px 50px;
  opacity: 0.5;
  pointer-events: none;
}

.page-header-content {
  max-width: var(--max-width);
  margin: 0 auto;
}

.page-header h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.page-header p {
  font-size: 1.2rem;
  max-width: 700px;
  margin: 0 auto;
  opacity: 0.9;
}

/* Common section styles */
.section-container {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0 2rem;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.section-divider {
  width: 80px;
  height: 4px;
  background-color: var(--primary-color);
  margin: 0 auto;
  border-radius: 2px;
}

/* Projects Section */
.projects-section {
  padding: 6rem 0;
 
}

/* Category Filter */
.category-filter {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
}

.category-button {
  padding: 0.6rem 1.5rem;
  background-color: transparent;
  border: 2px solid var(--border-color);
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-button:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(108, 143, 255, 0.15);
}

.category-button.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
  box-shadow: 0 5px 15px rgba(108, 143, 255, 0.3);
}

/* Dark theme specific styles */
.dark .category-button {
  border-color: rgba(108, 143, 255, 0.2);
}

.dark .category-button:hover {
  box-shadow: 0 5px 15px rgba(108, 143, 255, 0.2);
}

/* Projects Grid */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 0;
  color: var(--text-secondary);
  background-color: var(--bg-secondary);
  border-radius: 10px;
  margin: 2rem 0;
  border: 1px solid var(--border-color);
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
  color: var(--primary-color);
}

.empty-state h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

/* Dark theme specific styles */
.dark .empty-state {
  background-color: rgba(108, 143, 255, 0.05);
  border-color: rgba(108, 143, 255, 0.1);
}

/* Project Process Section */
.project-process-section {
  padding: 6rem 0;

}

.process-steps {
  max-width: 800px;
  margin: 0 auto;
}

.process-step {
  display: flex;
  margin-bottom: 3rem;
  position: relative;
}

.process-step:last-child {
  margin-bottom: 0;
}

.process-step::before {
  content: '';
  position: absolute;
  top: 70px;
  left: 30px;
  width: 2px;
  height: calc(100% - 30px);
  background-color: var(--border-color);
  z-index: 1;
}

.process-step:last-child::before {
  display: none;
}

.process-step-number {
  width: 60px;
  height: 60px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 700;
  margin-right: 2rem;
  position: relative;
  z-index: 2;
  flex-shrink: 0;
  box-shadow: 0 5px 15px rgba(108, 143, 255, 0.3);
  transition: all 0.3s ease;
}

.process-step:hover .process-step-number {
  transform: scale(1.1);
  box-shadow: 0 8px 20px rgba(108, 143, 255, 0.4);
}

.process-step-content {
  background-color: var(--card-bg);
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: var(--card-shadow);
  flex-grow: 1;
  border: 1px solid transparent;
  transition: all 0.3s ease;
}

.process-step:hover .process-step-content {
  transform: translateY(-5px);
  border-color: rgba(108, 143, 255, 0.3);
}

.process-step-content h3 {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: var(--text-primary);
}

.process-step-content p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Dark theme specific styles */
.dark .process-step::before {
  background-color: rgba(108, 143, 255, 0.2);
}

.dark .process-step-content {
  border-color: rgba(108, 143, 255, 0.1);
}

.dark .process-step:hover .process-step-content {
  box-shadow: 0 10px 25px rgba(108, 143, 255, 0.15);
  border-color: rgba(108, 143, 255, 0.3);
}

/* Collaboration Section */
.collaboration-section {
  padding: 6rem 0;
  
  color: white;
  position: relative;
  overflow: hidden;
}

/* Add a subtle pattern to the CTA section */
.collaboration-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 1%, transparent 5%),
                    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 1%, transparent 5%);
  background-size: 50px 50px;
  opacity: 0.5;
  pointer-events: none;
}

.collaboration-content {
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.collaboration-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.collaboration-content p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.contact-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  background-color: white;
  color: var(--primary-color);
  border-radius: 6px;
  font-size: 1.1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.2);
}

.contact-button:hover {
  background-color: rgba(255, 255, 255, 0.9);
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  text-decoration: none;
}

.contact-button i {
  margin-right: 0.5rem;
}

/* Responsive Styles */
@media screen and (max-width: 992px) {
  .page-header h1 {
    font-size: 2.5rem;
  }

  .section-title,
  .collaboration-content h2 {
    font-size: 2rem;
  }

  .process-step {
    flex-direction: column;
  }

  .process-step-number {
    margin-bottom: 1rem;
    margin-right: 0;
  }

  .process-step::before {
    display: none;
  }
}

@media screen and (max-width: 768px) {
  .section-container {
    padding: 0 1.5rem;
  }

  .category-filter {
    flex-wrap: nowrap;
    overflow-x: auto;
    padding-bottom: 1rem;
    justify-content: flex-start;
  }

  .category-button {
    white-space: nowrap;
  }

  .projects-grid {
    grid-template-columns: 1fr;
  }
}
