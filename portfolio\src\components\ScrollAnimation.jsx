import React, { useEffect, useRef, useState } from 'react';
import './ScrollAnimation.css';

function ScrollAnimation({ 
  children, 
  animation = 'fade-up', 
  delay = 0,
  threshold = 0.2,
  duration = 0.8,
  once = true
}) {
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef(null);
  
  useEffect(() => {
    const currentElement = elementRef.current;
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          if (once) {
            observer.unobserve(currentElement);
          }
        } else if (!once) {
          setIsVisible(false);
        }
      },
      { threshold }
    );
    
    if (currentElement) {
      observer.observe(currentElement);
    }
    
    return () => {
      if (currentElement) {
        observer.unobserve(currentElement);
      }
    };
  }, [threshold, once]);
  
  const animationStyle = {
    '--animation-delay': `${delay}s`,
    '--animation-duration': `${duration}s`
  };
  
  return (
    <div 
      ref={elementRef}
      className={`scroll-animation ${animation} ${isVisible ? 'animate' : ''}`}
      style={animationStyle}
    >
      {children}
    </div>
  );
}

export default ScrollAnimation;
