import React from 'react';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import SkillBar from '../components/SkillBar';
import ScrollAnimation from '../components/ScrollAnimation';
import { useTheme } from '../context/ThemeContext';
import './FlatSkillsPage.css';

function FlatSkillsPage() {
  const { theme } = useTheme();

  // Frontend skills
  const frontendSkills = [
    { name: 'HTML5', percentage: 90, color: '#E44D26' },
    { name: 'CSS3', percentage: 85, color: '#264DE4' },
    { name: 'JavaScript', percentage: 80, color: '#F7DF1E' },
    { name: 'React', percentage: 75, color: '#61DAFB' },
    { name: 'Bootstrap', percentage: 85, color: '#7952B3' },
  ];

  // Backend skills
  const backendSkills = [
    { name: 'Python', percentage: 85, color: '#3776AB' },
    { name: 'Django', percentage: 80, color: '#092E20' },
    { name: 'MySQL', percentage: 75, color: '#4479A1' },
    { name: 'SQLite', percentage: 80, color: '#003B57' },
    { name: 'C++', percentage: 70, color: '#00599C' },
  ];

  // Tools and other skills
  const otherSkills = [
    { name: 'Git & GitHub', percentage: 85, color: '#F05032' },
    { name: 'VS Code', percentage: 90, color: '#007ACC' },
    { name: 'Eclipse', percentage: 75, color: '#2C2255' },
    { name: 'AWS', percentage: 65, color: '#FF9900' },
  ];

  return (
    <div className={`skills-page ${theme}`}>
      <Navbar />

      {/* Page Header */}
      <header className="page-header">
        <div className="page-header-content">
         
          <ScrollAnimation animation="fade-up" delay={0.2}>
            <h1>My Skills</h1>
            <p>A comprehensive overview of my technical skills and expertise.</p>
          </ScrollAnimation>
        </div>
      </header>

      {/* Skills Overview Section */}
      <section className="skills-overview-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="skills-overview-content">
              <h2>My Expertise</h2>
              <p>
                As a Computer Science student and Software Developer Intern, I've cultivated a diverse set of skills across frontend and backend development.
                I'm passionate about creating clean, efficient, and maintainable code that delivers exceptional user experiences.
              </p>
              <p>
                I continuously strive to expand my knowledge and stay up-to-date with the latest technologies and best practices
                in the rapidly evolving field of web development. My coursework includes Data Structures, Algorithms, Database Management, and more.
              </p>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Frontend Skills Section */}
      <section className="skills-category-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="skills-category-header">
              <div className="skills-category-icon">
                <i className="fas fa-laptop-code"></i>
              </div>
              <h2>Frontend Development</h2>
              <p>
                I specialize in building responsive, interactive, and accessible user interfaces
                using modern frontend technologies including HTML, CSS, JavaScript, React, and Bootstrap.
              </p>
            </div>
          </ScrollAnimation>

          <ScrollAnimation animation="fade-up" delay={0.2}>
            <div className="skills-bars">
              {frontendSkills.map((skill, index) => (
                <SkillBar
                  key={index}
                  name={skill.name}
                  percentage={skill.percentage}
                  color={skill.color}
                />
              ))}
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Backend Skills Section */}
      <section className="skills-category-section alternate">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="skills-category-header">
              <div className="skills-category-icon">
                <i className="fas fa-server"></i>
              </div>
              <h2>Backend Development</h2>
              <p>
                I build robust server-side applications using Python and Django, with experience in
                database management using MySQL and SQLite to power dynamic web applications.
              </p>
            </div>
          </ScrollAnimation>

          <ScrollAnimation animation="fade-up" delay={0.2}>
            <div className="skills-bars">
              {backendSkills.map((skill, index) => (
                <SkillBar
                  key={index}
                  name={skill.name}
                  percentage={skill.percentage}
                  color={skill.color}
                />
              ))}
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Tools & Other Skills Section */}
      <section className="skills-category-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="skills-category-header">
              <div className="skills-category-icon">
                <i className="fas fa-tools"></i>
              </div>
              <h2>Tools & Other Skills</h2>
              <p>
                I leverage various development tools including VS Code, Eclipse, Git & GitHub, and AWS
                to streamline the development process and ensure code quality.
              </p>
            </div>
          </ScrollAnimation>

          <ScrollAnimation animation="fade-up" delay={0.2}>
            <div className="skills-bars">
              {otherSkills.map((skill, index) => (
                <SkillBar
                  key={index}
                  name={skill.name}
                  percentage={skill.percentage}
                  color={skill.color}
                />
              ))}
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Soft Skills Section */}
      <section className="soft-skills-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="section-header">
              <h2 className="section-title">Soft Skills</h2>
              <div className="section-divider"></div>
            </div>
          </ScrollAnimation>

          <div className="soft-skills-grid">
            <ScrollAnimation animation="fade-up" delay={0.1}>
              <div className="soft-skill-card">
                <div className="soft-skill-icon">
                  <i className="fas fa-comments"></i>
                </div>
                <h3>Leadership</h3>
                <p>
                  Strong leadership skills with the ability to guide teams and take initiative
                  in project management and problem-solving situations.
                </p>
              </div>
            </ScrollAnimation>

            <ScrollAnimation animation="fade-up" delay={0.2}>
              <div className="soft-skill-card">
                <div className="soft-skill-icon">
                  <i className="fas fa-users"></i>
                </div>
                <h3>Teamwork</h3>
                <p>
                  Collaborative approach to problem-solving and development, with experience
                  working in agile teams and cross-functional environments.
                </p>
              </div>
            </ScrollAnimation>

            <ScrollAnimation animation="fade-up" delay={0.3}>
              <div className="soft-skill-card">
                <div className="soft-skill-icon">
                  <i className="fas fa-lightbulb"></i>
                </div>
                <h3>Quick Learning</h3>
                <p>
                  Ability to quickly adapt to new technologies and frameworks, with a passion
                  for continuous learning and staying updated with industry trends.
                </p>
              </div>
            </ScrollAnimation>

            <ScrollAnimation animation="fade-up" delay={0.4}>
              <div className="soft-skill-card">
                <div className="soft-skill-icon">
                  <i className="fas fa-tasks"></i>
                </div>
                <h3>Presentation</h3>
                <p>
                  Strong presentation skills with the ability to effectively communicate
                  technical concepts and project outcomes to diverse audiences.
                </p>
              </div>
            </ScrollAnimation>
          </div>
        </div>
      </section>


      <Footer />
    </div>
  );
}

export default FlatSkillsPage;
