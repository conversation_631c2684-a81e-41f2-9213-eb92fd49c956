import React from 'react';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import SkillBar from '../components/SkillBar';
import ScrollAnimation from '../components/ScrollAnimation';
import { useTheme } from '../context/ThemeContext';
import './FlatSkillsPage.css';

function FlatSkillsPage() {
  const { theme } = useTheme();

  // Frontend skills
  const frontendSkills = [
    { name: 'HTML5', percentage: 95, color: '#E44D26' },
    { name: 'CSS3', percentage: 90, color: '#264DE4' },
    { name: 'JavaScript', percentage: 92, color: '#F7DF1E' },
    { name: 'React', percentage: 88, color: '#61DAFB' },
    { name: 'Responsive Design', percentage: 95, color: '#FF6B6B' },
  ];

  // Backend skills
  const backendSkills = [
    { name: 'Django', percentage: 85, color: '#339933' },
    { name: 'DjangoRest Framework', percentage: 82, color: '#000000' },
    { name: 'MongoDB', percentage: 78, color: '#47A248' },
    { name: 'SQL', percentage: 75, color: '#4479A1' },
    { name: 'RESTful APIs', percentage: 90, color: '#FF9900' },
  ];

  // Tools and other skills
  const otherSkills = [
    { name: 'Git & GitHub', percentage: 92, color: '#F05032' },
    { name: 'AWS', percentage: 70, color: '#FF9900' },
  ];

  return (
    <div className={`skills-page ${theme}`}>
      <Navbar />

      {/* Page Header */}
      <header className="page-header">
        <div className="page-header-content">
          <ScrollAnimation animation="fade-up">
            <div className="profile-image-container">
              {/* Replace with your image */}
              <img src="https://via.placeholder.com/300" alt="John Doe" className="profile-image" />
            </div>
          </ScrollAnimation>
          <ScrollAnimation animation="fade-up" delay={0.2}>
            <h1>My Skills</h1>
            <p>A comprehensive overview of my technical skills and expertise.</p>
          </ScrollAnimation>
        </div>
      </header>

      {/* Skills Overview Section */}
      <section className="skills-overview-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="skills-overview-content">
              <h2>My Expertise</h2>
              <p>
                As a full-stack developer, I've cultivated a diverse set of skills across the entire web development spectrum.
                I'm passionate about creating clean, efficient, and maintainable code that delivers exceptional user experiences.
              </p>
              <p>
                I continuously strive to expand my knowledge and stay up-to-date with the latest technologies and best practices
                in the rapidly evolving field of web development.
              </p>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Frontend Skills Section */}
      <section className="skills-category-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="skills-category-header">
              <div className="skills-category-icon">
                <i className="fas fa-laptop-code"></i>
              </div>
              <h2>Frontend Development</h2>
              <p>
                I specialize in building responsive, interactive, and accessible user interfaces
                using modern frontend technologies and frameworks.
              </p>
            </div>
          </ScrollAnimation>

          <ScrollAnimation animation="fade-up" delay={0.2}>
            <div className="skills-bars">
              {frontendSkills.map((skill, index) => (
                <SkillBar
                  key={index}
                  name={skill.name}
                  percentage={skill.percentage}
                  color={skill.color}
                />
              ))}
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Backend Skills Section */}
      <section className="skills-category-section alternate">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="skills-category-header">
              <div className="skills-category-icon">
                <i className="fas fa-server"></i>
              </div>
              <h2>Backend Development</h2>
              <p>
                I build robust server-side applications, RESTful APIs, and database solutions
                to power dynamic web applications.
              </p>
            </div>
          </ScrollAnimation>

          <ScrollAnimation animation="fade-up" delay={0.2}>
            <div className="skills-bars">
              {backendSkills.map((skill, index) => (
                <SkillBar
                  key={index}
                  name={skill.name}
                  percentage={skill.percentage}
                  color={skill.color}
                />
              ))}
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Tools & Other Skills Section */}
      <section className="skills-category-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="skills-category-header">
              <div className="skills-category-icon">
                <i className="fas fa-tools"></i>
              </div>
              <h2>Tools & Other Skills</h2>
              <p>
                I leverage various development tools, deployment platforms, and methodologies
                to streamline the development process and ensure code quality.
              </p>
            </div>
          </ScrollAnimation>

          <ScrollAnimation animation="fade-up" delay={0.2}>
            <div className="skills-bars">
              {otherSkills.map((skill, index) => (
                <SkillBar
                  key={index}
                  name={skill.name}
                  percentage={skill.percentage}
                  color={skill.color}
                />
              ))}
            </div>
          </ScrollAnimation>
        </div>
      </section>

      {/* Soft Skills Section */}
      <section className="soft-skills-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="section-header">
              <h2 className="section-title">Soft Skills</h2>
              <div className="section-divider"></div>
            </div>
          </ScrollAnimation>

          <div className="soft-skills-grid">
            <ScrollAnimation animation="fade-up" delay={0.1}>
              <div className="soft-skill-card">
                <div className="soft-skill-icon">
                  <i className="fas fa-comments"></i>
                </div>
                <h3>Communication</h3>
                <p>
                  Clear and effective communication with team members, stakeholders, and clients
                  to ensure project requirements are understood and met.
                </p>
              </div>
            </ScrollAnimation>

            <ScrollAnimation animation="fade-up" delay={0.2}>
              <div className="soft-skill-card">
                <div className="soft-skill-icon">
                  <i className="fas fa-users"></i>
                </div>
                <h3>Teamwork</h3>
                <p>
                  Collaborative approach to problem-solving and development, with experience
                  working in agile teams and cross-functional environments.
                </p>
              </div>
            </ScrollAnimation>

            <ScrollAnimation animation="fade-up" delay={0.3}>
              <div className="soft-skill-card">
                <div className="soft-skill-icon">
                  <i className="fas fa-lightbulb"></i>
                </div>
                <h3>Problem Solving</h3>
                <p>
                  Analytical thinking and creative problem-solving skills to tackle complex
                  technical challenges and find efficient solutions.
                </p>
              </div>
            </ScrollAnimation>

            <ScrollAnimation animation="fade-up" delay={0.4}>
              <div className="soft-skill-card">
                <div className="soft-skill-icon">
                  <i className="fas fa-tasks"></i>
                </div>
                <h3>Time Management</h3>
                <p>
                  Efficient prioritization and organization of tasks to meet deadlines and
                  deliver high-quality work within project timelines.
                </p>
              </div>
            </ScrollAnimation>
          </div>
        </div>
      </section>

      
      <Footer />
    </div>
  );
}

export default FlatSkillsPage;
