import React from 'react';
import { Link } from 'react-router-dom';
import { useTheme } from '../context/ThemeContext';
import ScrollAnimation from './ScrollAnimation';
import './Footer.css';

function Footer() {
  const currentYear = new Date().getFullYear();
  const { theme } = useTheme();

  return (
    <footer className={`footer ${theme}`}>
      <div className="footer-container">
        <div className="footer-top">
          <ScrollAnimation animation="fade-right" delay={0.1}>
            <div className="footer-logo">
              <Link to="/" className="footer-logo-link">
                <span className="logo-text">Divy</span>
                <span className="logo-highlight">Patel</span>
              </Link>
              <p className="footer-tagline">Building responsive web interfaces with passion</p>
            </div>
          </ScrollAnimation>

          <div className="footer-links">
            <ScrollAnimation animation="fade-up" delay={0.2}>
              <div className="footer-links-section">
                <h3>Navigation</h3>
                <ul>
                  <li><Link to="/">Home</Link></li>
                  <li><Link to="/about">About</Link></li>
                  <li><Link to="/skills">Skills</Link></li>
                  <li><Link to="/projects">Projects</Link></li>
                  <li><Link to="/contact">Contact</Link></li>
                </ul>
              </div>
            </ScrollAnimation>

            <ScrollAnimation animation="fade-up" delay={0.3}>
              <div className="footer-links-section">
                <h3>Connect</h3>
                <ul>
                  <li><a href="https://github.com/Divy2003" target="_blank" rel="noopener noreferrer">GitHub</a></li>
                  <li><a href="https://www.linkedin.com/in/divy-patel-371a01249/" target="_blank" rel="noopener noreferrer">LinkedIn</a></li>
                  <li><a href="tel:+918200365874">Phone</a></li>
                  <li><a href="mailto:<EMAIL>">Email</a></li>
                </ul>
              </div>
            </ScrollAnimation>
          </div>
        </div>

        <ScrollAnimation animation="fade-up" delay={0.4}>
          <div className="footer-bottom">
            <p className="copyright">© {currentYear} Divy Patel. All rights reserved.</p>
            <div className="social-icons">
              <a href="https://github.com/Divy2003" target="_blank" rel="noopener noreferrer" aria-label="GitHub" className="social-icon">
                <i className="fab fa-github"></i>
              </a>
              <a href="https://www.linkedin.com/in/divy-patel-371a01249/" target="_blank" rel="noopener noreferrer" aria-label="LinkedIn" className="social-icon">
                <i className="fab fa-linkedin"></i>
              </a>
              <a href="mailto:<EMAIL>" aria-label="Email" className="social-icon">
                <i className="fas fa-envelope"></i>
              </a>
              <a href="tel:+918200365874" aria-label="Phone" className="social-icon">
                <i className="fas fa-phone"></i>
              </a>
            </div>
          </div>
        </ScrollAnimation>
      </div>
    </footer>
  );
}

export default Footer;
