.theme-toggle {
  position: relative;
  z-index: 100;
}

.theme-toggle-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  outline: none;
}

.toggle-track {
  position: relative;
  width: 50px;
  height: 24px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 4px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) inset;
}

.dark .toggle-track {
  background-color: rgba(255, 255, 255, 0.1);
}

.toggle-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  z-index: 1;
}

.toggle-icon.sun {
  color: #f1c40f;
}

.toggle-icon.moon {
  color: #f1f1f1;
}

.toggle-thumb {
  position: absolute;
  width: 20px;
  height: 20px;
  background-color: #fff;
  border-radius: 50%;
  top: 2px;
  left: 2px;
  transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.theme-toggle-button.dark .toggle-thumb {
  transform: translateX(26px);
  background-color: #2c3e50;
}

/* Hover effect */
.toggle-track:hover {
  opacity: 0.9;
}

/* Focus styles for accessibility */
.theme-toggle-button:focus .toggle-track {
  box-shadow: 0 0 0 2px var(--primary-color);
}
