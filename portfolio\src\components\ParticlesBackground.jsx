import React, { useEffect, useRef } from 'react';
import { useTheme } from '../context/ThemeContext';
import './ParticlesBackground.css';

function ParticlesBackground() {
  const canvasRef = useRef(null);
  const { theme } = useTheme();

  useEffect(() => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    let animationFrameId;
    let particles = [];
    let stars = [];

    // Create a space-like background
    function drawBackground() {
      // Create a gradient for the space background
      const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
      gradient.addColorStop(0, '#0f0f1a');
      gradient.addColorStop(0.5, '#121824');
      gradient.addColorStop(1, '#0a0a14');

      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Draw stars
      stars.forEach(star => {
        const flickerIntensity = Math.random() * 0.5 + 0.5;

        // Add glow effect to stars
        const glow = ctx.createRadialGradient(
          star.x, star.y, 0,
          star.x, star.y, star.size * 2
        );
        glow.addColorStop(0, `rgba(255, 255, 255, ${star.opacity * flickerIntensity})`);
        glow.addColorStop(1, 'transparent');

        ctx.fillStyle = glow;
        ctx.beginPath();
        ctx.arc(star.x, star.y, star.size * 2, 0, Math.PI * 2);
        ctx.fill();

        // Draw star center
        ctx.fillStyle = `rgba(255, 255, 255, ${star.opacity * flickerIntensity})`;
        ctx.beginPath();
        ctx.arc(star.x, star.y, star.size * 0.5, 0, Math.PI * 2);
        ctx.fill();
      });
    }

    // Initialize stars
    function initStars() {
      stars = [];
      const starCount = Math.floor(canvas.width * canvas.height / 2000); // More stars

      for (let i = 0; i < starCount; i++) {
        // Create different types of stars
        const starType = Math.random();
        let size, opacity;

        if (starType < 0.7) {
          // Small distant stars (majority)
          size = Math.random() * 1.2;
          opacity = Math.random() * 0.5 + 0.2;
        } else if (starType < 0.95) {
          // Medium stars
          size = Math.random() * 1.5 + 1.2;
          opacity = Math.random() * 0.3 + 0.5;
        } else {
          // Bright stars (few)
          size = Math.random() * 2 + 2;
          opacity = Math.random() * 0.2 + 0.8;
        }

        stars.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          size: size,
          opacity: opacity
        });
      }
    }

    // Particle class
    class Particle {
      constructor() {
        this.x = Math.random() * canvas.width;
        this.y = Math.random() * canvas.height;
        this.size = Math.random() * 3 + 1; // Larger particles
        this.speedX = Math.random() * 0.3 - 0.15;
        this.speedY = Math.random() * 0.3 - 0.15;
        this.pulseSpeed = Math.random() * 0.01 + 0.005; // For pulsing effect
        this.pulseDirection = 1;
        this.pulseSize = 0;
        this.maxPulse = Math.random() * 0.5 + 0.5;

        // Create more varied colors for particles
        const colorType = Math.random();
        let hue, saturation, lightness, alpha;

        if (colorType < 0.6) {
          // Blue-ish particles (majority)
          hue = 210 + Math.random() * 30;
          saturation = 70 + Math.random() * 30;
          lightness = 60 + Math.random() * 20;
          alpha = Math.random() * 0.3 + 0.5;
        } else if (colorType < 0.85) {
          // Purple-ish particles
          hue = 260 + Math.random() * 30;
          saturation = 70 + Math.random() * 30;
          lightness = 60 + Math.random() * 20;
          alpha = Math.random() * 0.3 + 0.5;
        } else {
          // Cyan-ish particles
          hue = 180 + Math.random() * 30;
          saturation = 70 + Math.random() * 30;
          lightness = 60 + Math.random() * 20;
          alpha = Math.random() * 0.3 + 0.5;
        }

        this.color = `hsla(${hue}, ${saturation}%, ${lightness}%, ${alpha})`;
        this.coreColor = `hsla(${hue}, ${saturation}%, ${lightness + 20}%, 0.9)`;
      }

      update() {
        this.x += this.speedX;
        this.y += this.speedY;

        // Wrap around edges
        if (this.x < 0) this.x = canvas.width;
        if (this.x > canvas.width) this.x = 0;
        if (this.y < 0) this.y = canvas.height;
        if (this.y > canvas.height) this.y = 0;

        // Pulsing effect
        this.pulseSize += this.pulseSpeed * this.pulseDirection;
        if (this.pulseSize > this.maxPulse || this.pulseSize < 0) {
          this.pulseDirection *= -1;
        }
      }

      draw() {
        // Draw outer glow effect
        const outerGlow = ctx.createRadialGradient(
          this.x, this.y, 0,
          this.x, this.y, this.size * (3 + this.pulseSize)
        );
        outerGlow.addColorStop(0, this.color);
        outerGlow.addColorStop(1, 'transparent');

        ctx.fillStyle = outerGlow;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size * (3 + this.pulseSize), 0, Math.PI * 2);
        ctx.fill();

        // Draw inner glow
        const innerGlow = ctx.createRadialGradient(
          this.x, this.y, 0,
          this.x, this.y, this.size * 1.5
        );
        innerGlow.addColorStop(0, this.coreColor);
        innerGlow.addColorStop(1, 'transparent');

        ctx.fillStyle = innerGlow;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size * 1.5, 0, Math.PI * 2);
        ctx.fill();

        // Draw particle core
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size * 0.5, 0, Math.PI * 2);
        ctx.fill();
      }
    }

    // Initialize particles
    function initParticles() {
      particles = [];
      // Create more particles for a denser network
      const particleCount = Math.min(Math.floor(canvas.width * canvas.height / 12000), 150);

      for (let i = 0; i < particleCount; i++) {
        particles.push(new Particle());
      }
    }

    // Connect particles with lines
    function connectParticles() {
      const maxDistance = 180; // Increased max distance

      for (let i = 0; i < particles.length; i++) {
        for (let j = i; j < particles.length; j++) {
          const dx = particles[i].x - particles[j].x;
          const dy = particles[i].y - particles[j].y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < maxDistance) {
            const opacity = 1 - (distance / maxDistance);

            // Create a gradient line with glow effect
            const gradient = ctx.createLinearGradient(
              particles[i].x, particles[i].y,
              particles[j].x, particles[j].y
            );

            // More vibrant blue colors
            gradient.addColorStop(0, `rgba(100, 149, 237, ${opacity * 0.7})`);
            gradient.addColorStop(0.5, `rgba(65, 105, 225, ${opacity * 0.7})`);
            gradient.addColorStop(1, `rgba(30, 144, 255, ${opacity * 0.7})`);

            // Draw glow effect first (wider line)
            ctx.strokeStyle = `rgba(70, 130, 230, ${opacity * 0.3})`;
            ctx.lineWidth = opacity * 4;
            ctx.beginPath();
            ctx.moveTo(particles[i].x, particles[i].y);
            ctx.lineTo(particles[j].x, particles[j].y);
            ctx.stroke();

            // Draw main line
            ctx.strokeStyle = gradient;
            ctx.lineWidth = opacity * 1.5;
            ctx.beginPath();
            ctx.moveTo(particles[i].x, particles[i].y);
            ctx.lineTo(particles[j].x, particles[j].y);
            ctx.stroke();
          }
        }
      }
    }

    // Animation loop
    function animate() {
      // Clear canvas completely each frame for a clean look
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Draw background and stars
      drawBackground();

      // Draw and update particles
      particles.forEach(particle => {
        particle.update();
        particle.draw();
      });

      // Connect particles with lines
      connectParticles();

      // Add a subtle overlay to enhance the space effect
      const overlay = ctx.createRadialGradient(
        canvas.width / 2, canvas.height / 2, 0,
        canvas.width / 2, canvas.height / 2, canvas.width
      );
      overlay.addColorStop(0, 'rgba(10, 10, 30, 0)');
      overlay.addColorStop(1, 'rgba(10, 10, 30, 0.3)');

      ctx.fillStyle = overlay;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      animationFrameId = requestAnimationFrame(animate);
    }

    // Set canvas size
    const handleResize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      initStars();
      initParticles();
    };

    window.addEventListener('resize', handleResize);
    handleResize();

    // Start animation
    animate();

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      cancelAnimationFrame(animationFrameId);
    };
  }, [theme]);

  return <canvas ref={canvasRef} className="particles-canvas"></canvas>;
}

export default ParticlesBackground;
