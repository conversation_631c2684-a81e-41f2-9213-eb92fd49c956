.home-page {
  width: 100%;
  overflow-x: hidden;
}

/* Common section styles */
.section-container {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0 2rem;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.section-divider {
  width: 80px;
  height: 4px;
  background-color: var(--primary-color);
  margin: 0 auto;
  border-radius: 2px;
}

/* Hero Section */
.hero-section {
  min-height: 100vh;
  padding: 6rem 2rem;
 
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  max-width: 800px;
  width: 100%;
  z-index: 2;
}

.profile-image-container {
  margin-bottom: 2rem;
  position: relative;
}

.profile-image {
  width: 180px;
  height: 180px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid var(--primary-color);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.profile-image:hover {
  transform: scale(1.05);
  box-shadow: 0 15px 40px rgba(108, 143, 255, 0.3);
}

/* Add a glow effect to the profile image in dark mode */
.dark .profile-image {
  box-shadow: 0 10px 30px rgba(108, 143, 255, 0.2);
}

.dark .profile-image:hover {
  box-shadow: 0 15px 40px rgba(108, 143, 255, 0.4);
}

.hero-content {
  text-align: center;
  max-width: 700px;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.hero-title .highlight {
  color: var(--primary-color);
  position: relative;
  display: inline-block;
}

.hero-title .highlight::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 8px;
  bottom: 5px;
  left: 0;
  background-color: rgba(74, 107, 255, 0.2);
  z-index: -1;
}

/* Dark theme highlight effect */
.dark .hero-title .highlight::after {
  background-color: rgba(108, 143, 255, 0.3);
}

.hero-subtitle {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  min-height: 2.7rem; /* Keep height stable during typewriter animation */
}

.hero-description {
  font-size: 1.1rem;
  line-height: 1.7;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* About Preview Section */
.about-preview-section {
  padding: 6rem 0;

}



.about-preview-text {
  flex: 2;
  min-width: 300px;
}

.about-preview-text p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

.about-preview-stats {
  flex: 1;
  min-width: 250px;
  display: flex;
  
  gap: 2rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
  background-color: var(--bg-secondary);
  border-radius: 10px;
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.stat-item:hover {
  transform: translateY(-5px);
  border-color: var(--primary-color);
}

/* Dark theme glow effect */
.dark .stat-item:hover {
  box-shadow: 0 10px 25px rgba(108, 143, 255, 0.15);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1rem;
  color: var(--text-secondary);
}

/* Skills Preview Section */
.skills-preview-section {
  padding: 6rem 0;

}

.skills-preview-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.skills-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 3rem;
  width: 100%;
}

.skill-category {
  flex: 1;
  min-width: 250px;
  max-width: 350px;
  background-color: var(--card-bg);
  padding: 2rem;
  border-radius: 10px;
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.skill-category:hover {
  transform: translateY(-10px);
  border-color: var(--primary-color);
}

/* Dark theme glow effect */
.dark .skill-category:hover {
  box-shadow: 0 15px 35px rgba(108, 143, 255, 0.15);
}

.skill-category h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--primary-color);
  position: relative;
}

.skill-category h3::after {
  content: '';
  position: absolute;
  width: 40px;
  height: 3px;
  background-color: var(--primary-color);
  bottom: -8px;
  left: 0;
  border-radius: 2px;
}

.skill-category ul {
  list-style: none;
  padding: 0;
}

.skill-category ul li {
  margin-bottom: 1rem;
  font-size: 1.1rem;
  color: var(--text-secondary);
}

.skill-category ul li i {
  margin-right: 0.5rem;
  color: var(--primary-color);
}

/* Projects Preview Section */
.projects-preview-section {
  padding: 6rem 0;

}

.projects-preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.project-preview-card {
  background-color: var(--card-bg);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.project-preview-card:hover {
  transform: translateY(-10px);
  border-color: var(--primary-color);
}

/* Dark theme glow effect */
.dark .project-preview-card:hover {
  box-shadow: 0 15px 35px rgba(108, 143, 255, 0.15);
}

.project-preview-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  position: relative;
}

.project-preview-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, transparent 70%, rgba(0, 0, 0, 0.2) 100%);
  pointer-events: none;
}

.project-preview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.project-preview-card:hover .project-preview-image img {
  transform: scale(1.05);
}

.project-preview-content {
  padding: 1.5rem;
}

.project-preview-content h3 {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: var(--text-primary);
}

.project-preview-content p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.project-preview-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.project-preview-tags span {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.project-preview-tags span:hover {
  background-color: var(--primary-color);
  color: white;
}

.projects-cta, .skills-cta {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

/* Contact CTA Section */
.contact-cta-section {
  padding: 6rem 0;

  color: white;
  position: relative;
  overflow: hidden;
}

/* Add a subtle pattern to the CTA section */
.contact-cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 1%, transparent 5%),
                    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 1%, transparent 5%);
  background-size: 50px 50px;
  opacity: 0.5;
  pointer-events: none;
}

.contact-cta-content {
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.contact-cta-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.contact-cta-content p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.contact-cta-content .button.primary {
  background-color: white;
  color: var(--primary-color);
}

.contact-cta-content .button.primary:hover {
  background-color: rgba(255, 255, 255, 0.9);
}

/* Responsive Styles */
@media screen and (max-width: 992px) {
  .hero-section {
    padding-top: 120px;
    padding-bottom: 4rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .about-preview-content {
    flex-direction: column;
  }
}

@media screen and (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.5rem;
  }

  .section-container {
    padding: 0 1.5rem;
  }

  .skills-grid, .projects-preview-grid {
    grid-template-columns: 1fr;
  }

  .contact-cta-content h2 {
    font-size: 2rem;
  }

  .contact-cta-content p {
    font-size: 1rem;
  }

  .profile-image {
    width: 150px;
    height: 150px;
  }
}

@media screen and (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .hero-buttons {
    flex-direction: column;
    width: 100%;
  }

  .hero-buttons .button {
    width: 100%;
  }

  .profile-image {
    width: 120px;
    height: 120px;
  }
}

/* Animation for dark mode */
@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(108, 143, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(108, 143, 255, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(108, 143, 255, 0.5);
  }
}

.dark .project-preview-card:hover,
.dark .skill-category:hover,
.dark .stat-item:hover {
  animation: glow 2s infinite;
}
