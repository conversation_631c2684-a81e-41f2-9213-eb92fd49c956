import React from 'react';
import { useTheme } from '../context/ThemeContext';
import './ThemeToggle.css';

function ThemeToggle() {
  const { theme, toggleTheme } = useTheme();
  
  return (
    <div className="theme-toggle">
      <button 
        className={`theme-toggle-button ${theme}`} 
        onClick={toggleTheme}
        aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
      >
        <div className="toggle-track">
          <div className="toggle-icon sun">
            <i className="fas fa-sun"></i>
          </div>
          <div className="toggle-icon moon">
            <i className="fas fa-moon"></i>
          </div>
          <div className="toggle-thumb"></div>
        </div>
      </button>
    </div>
  );
}

export default ThemeToggle;
