import React from 'react';
import { Link } from 'react-router-dom';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import Button from '../components/Button';
import ScrollAnimation from '../components/ScrollAnimation';
import { useTheme } from '../context/ThemeContext';
import './FlatNotFoundPage.css';

function FlatNotFoundPage() {
  const { theme } = useTheme();

  return (
    <div className={`not-found-page ${theme}`}>
      <Navbar />

      <div className="not-found-container">
        <ScrollAnimation animation="fade-up">
          <div className="not-found-content">
            <h1 className="not-found-title">404</h1>
            <h2 className="not-found-subtitle">Page Not Found</h2>
            <p className="not-found-description">
              Oops! The page you are looking for doesn't exist or has been moved.
            </p>
            <div className="not-found-actions">
              <Button to="/" type="primary" size="large">
                Back to Home
              </Button>
              <Button to="/projects" type="secondary" size="large">
                View Projects
              </Button>
            </div>
          </div>
        </ScrollAnimation>
        <ScrollAnimation animation="fade-up" delay={0.2}>
          <div className="not-found-image">
            <img src="https://via.placeholder.com/600x400" alt="404 Illustration" />
          </div>
        </ScrollAnimation>
      </div>

      <Footer />
    </div>
  );
}

export default FlatNotFoundPage;
