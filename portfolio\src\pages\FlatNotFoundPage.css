.not-found-page {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

.not-found-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6rem 2rem;
  max-width: var(--max-width);
  margin: 0 auto;
  gap: 4rem;
  position: relative;
  z-index: 1;
}

.not-found-content {
  flex: 1;
  max-width: 500px;
  position: relative;
}

.not-found-title {
  font-size: 10rem;
  font-weight: 800;
  color: var(--primary-color);
  line-height: 1;
  margin-bottom: 1rem;
  text-shadow: 0 0 20px rgba(108, 143, 255, 0.5);
  animation: glow 3s infinite alternate;
}

@keyframes glow {
  0% {
    text-shadow: 0 0 20px rgba(108, 143, 255, 0.5);
  }
  100% {
    text-shadow: 0 0 30px rgba(108, 143, 255, 0.8), 0 0 40px rgba(108, 143, 255, 0.4);
  }
}

.not-found-subtitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

.not-found-description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.not-found-actions {
  display: flex;
  gap: 1rem;
}

.not-found-image {
  flex: 1;
  max-width: 500px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.not-found-image::before {
  content: '';
  position: absolute;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(108, 143, 255, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  z-index: -1;
  animation: pulse 4s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.2;
  }
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
}

.not-found-image img {
  width: 100%;
  height: auto;
  max-width: 400px;
  filter: drop-shadow(0 0 15px rgba(108, 143, 255, 0.3));
  transition: all 0.5s ease;
}

.not-found-image img:hover {
  transform: translateY(-10px);
  filter: drop-shadow(0 0 20px rgba(108, 143, 255, 0.5));
}

@media screen and (max-width: 992px) {
  .not-found-container {
    flex-direction: column-reverse;
    text-align: center;
    gap: 2rem;
  }

  .not-found-title {
    font-size: 6rem;
  }

  .not-found-subtitle {
    font-size: 2rem;
  }

  .not-found-actions {
    justify-content: center;
  }
}

@media screen and (max-width: 768px) {
  .not-found-title {
    font-size: 5rem;
  }

  .not-found-actions {
    flex-direction: column;
  }
}
