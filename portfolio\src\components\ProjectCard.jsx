import React from 'react';
import Button from './Button';
import './ProjectCard.css';

function ProjectCard({ 
  title, 
  description, 
  image, 
  tags = [], 
  demoLink, 
  codeLink 
}) {
  return (
    <div className="project-card">
      <div className="project-image">
        <img src={image} alt={title} />
        <div className="project-overlay">
          <div className="project-buttons">
            {demoLink && (
              <Button href={demoLink} type="primary" size="small">
                <i className="fas fa-external-link-alt"></i> Live Demo
              </Button>
            )}
            {codeLink && (
              <Button href={codeLink} type="secondary" size="small">
                <i className="fas fa-code"></i> View Code
              </Button>
            )}
          </div>
        </div>
      </div>
      <div className="project-content">
        <h3 className="project-title">{title}</h3>
        <p className="project-description">{description}</p>
        <div className="project-tags">
          {tags.map((tag, index) => (
            <span key={index} className="project-tag">
              {tag}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
}

export default ProjectCard;
