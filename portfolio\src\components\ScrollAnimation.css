.scroll-animation {
  opacity: 0;
  will-change: transform, opacity;
  transition-property: transform, opacity;
  transition-duration: var(--animation-duration, 0.8s);
  transition-timing-function: cubic-bezier(0.25, 0.1, 0.25, 1);
  transition-delay: var(--animation-delay, 0s);
}

/* Fade animations */
.scroll-animation.fade-up {
  transform: translateY(30px);
}

.scroll-animation.fade-down {
  transform: translateY(-30px);
}

.scroll-animation.fade-left {
  transform: translateX(30px);
}

.scroll-animation.fade-right {
  transform: translateX(-30px);
}

.scroll-animation.zoom-in {
  transform: scale(0.9);
}

.scroll-animation.zoom-out {
  transform: scale(1.1);
}

/* Animated state */
.scroll-animation.animate {
  opacity: 1;
  transform: translate(0) scale(1);
}

/* Staggered animations for children */
.scroll-animation.stagger > * {
  opacity: 0;
  transform: translateY(20px);
  transition-property: transform, opacity;
  transition-duration: 0.6s;
  transition-timing-function: cubic-bezier(0.25, 0.1, 0.25, 1);
}

.scroll-animation.stagger.animate > * {
  opacity: 1;
  transform: translateY(0);
}

.scroll-animation.stagger.animate > *:nth-child(1) { transition-delay: calc(var(--animation-delay) + 0.1s); }
.scroll-animation.stagger.animate > *:nth-child(2) { transition-delay: calc(var(--animation-delay) + 0.2s); }
.scroll-animation.stagger.animate > *:nth-child(3) { transition-delay: calc(var(--animation-delay) + 0.3s); }
.scroll-animation.stagger.animate > *:nth-child(4) { transition-delay: calc(var(--animation-delay) + 0.4s); }
.scroll-animation.stagger.animate > *:nth-child(5) { transition-delay: calc(var(--animation-delay) + 0.5s); }
.scroll-animation.stagger.animate > *:nth-child(6) { transition-delay: calc(var(--animation-delay) + 0.6s); }
.scroll-animation.stagger.animate > *:nth-child(7) { transition-delay: calc(var(--animation-delay) + 0.7s); }
.scroll-animation.stagger.animate > *:nth-child(8) { transition-delay: calc(var(--animation-delay) + 0.8s); }
.scroll-animation.stagger.animate > *:nth-child(9) { transition-delay: calc(var(--animation-delay) + 0.9s); }
.scroll-animation.stagger.animate > *:nth-child(10) { transition-delay: calc(var(--animation-delay) + 1s); }
