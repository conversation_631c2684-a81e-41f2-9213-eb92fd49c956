.footer {

  padding: 4rem 0 2rem;
  color: var(--text-primary);
  border-top: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
}

/* Dark theme footer background effect */
.footer.dark::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 10% 10%, rgba(108, 143, 255, 0.05) 0%, transparent 20%),
    radial-gradient(circle at 90% 90%, rgba(108, 143, 255, 0.05) 0%, transparent 20%);
  pointer-events: none;
}

.footer-container {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.footer-top {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 3rem;
}

.footer-logo {
  flex: 1;
  min-width: 250px;
  margin-bottom: 2rem;
}

.footer-logo-link {
  font-size: 1.8rem;
  font-weight: 700;
  text-decoration: none;
  color: var(--text-primary);
  margin-bottom: 1rem;
  display: inline-block;
}

.logo-text {
  color: var(--text-primary);
}

.logo-highlight {
  color: var(--primary-color);
}

.footer-tagline {
  font-size: 1rem;
  color: var(--text-secondary);
  margin-top: 0.5rem;
}

.footer-links {
  display: flex;
  flex-wrap: wrap;
  flex: 2;
  justify-content: space-around;
}

.footer-links-section {
  margin-bottom: 2rem;
  min-width: 150px;
}

.footer-links-section h3 {
  font-size: 1.2rem;
  margin-bottom: 1.2rem;
  font-weight: 600;
  position: relative;
  color: var(--text-primary);
}

.footer-links-section h3::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -8px;
  width: 30px;
  height: 2px;
  background-color: var(--primary-color);
}

.footer-links-section ul {
  list-style: none;
  padding: 0;
}

.footer-links-section ul li {
  margin-bottom: 0.8rem;
}

.footer-links-section ul li a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  display: inline-block;
}

.footer-links-section ul li a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 1px;
  bottom: -2px;
  left: 0;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

.footer-links-section ul li a:hover {
  color: var(--primary-color);
}

.footer-links-section ul li a:hover::after {
  width: 100%;
}

.footer-bottom {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
}

.copyright {
  font-size: 0.9rem;
  color: var(--text-tertiary);
}

.social-icons {
  display: flex;
  gap: 1rem;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.social-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--primary-color);
  transform: translateY(100%);
  transition: transform 0.3s ease;
  z-index: 0;
}

.social-icon:hover {
  color: white;
  transform: translateY(-3px);
}

.social-icon:hover::before {
  transform: translateY(0);
}

.social-icons i {
  font-size: 1.2rem;
  position: relative;
  z-index: 1;
}

/* Dark theme specific styles */
.dark .social-icon {
  background-color: rgba(255, 255, 255, 0.1);
}

.dark .social-icon:hover {
  box-shadow: 0 0 15px rgba(108, 143, 255, 0.5);
}

@media screen and (max-width: 768px) {
  .footer-top {
    flex-direction: column;
  }

  .footer-links {
    flex-direction: column;
    margin-top: 2rem;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .social-icons {
    justify-content: center;
  }
}
