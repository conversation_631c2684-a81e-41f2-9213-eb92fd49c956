.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  z-index: 1000;
  transition: all 0.3s ease-in-out;
}

.navbar.scrolled {
  background-color: var(--navbar-bg);
  box-shadow: var(--navbar-shadow);
  height: 70px;
}

.navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: var(--max-width);
  padding: 0 2rem;
}

.navbar-logo {
  display: flex;
  align-items: center;
  font-size: 1.8rem;
  font-weight: 700;
  text-decoration: none;
  color: var(--text-primary);
}

.logo-text {
  color: var(--text-primary);
}

.logo-highlight {
  color: var(--primary-color);
}

.nav-menu {
  display: flex;
  list-style: none;
  text-align: center;
  margin: 0;
  padding: 0;
}

.nav-item {
  height: 80px;
  display: flex;
  align-items: center;
}

.theme-toggle-item {
  margin-left: 1rem;
}

.nav-link {
  color: var(--text-primary);
  display: flex;
  align-items: center;
  text-decoration: none;
  padding: 0 1rem;
  height: 100%;
  font-weight: 500;
  position: relative;
  transition: all 0.3s ease;
}

.nav-link:hover {
  color: var(--primary-color);
  text-decoration: none;
}

.nav-link.active {
  color: var(--primary-color);
  font-weight: 600;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: 25px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

.menu-icon {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 21px;
  cursor: pointer;
  z-index: 10;
}

.menu-icon span {
  display: block;
  height: 3px;
  width: 100%;
  background-color: var(--text-primary);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.menu-icon.active span:nth-child(1) {
  transform: translateY(9px) rotate(45deg);
}

.menu-icon.active span:nth-child(2) {
  opacity: 0;
}

.menu-icon.active span:nth-child(3) {
  transform: translateY(-9px) rotate(-45deg);
}

/* Dark theme specific styles */
.navbar.dark.scrolled {
  background-color: rgba(18, 18, 18, 0.95);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

@media screen and (max-width: 960px) {
  .nav-menu {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100vh;
    position: absolute;
    top: 0;
    left: -100%;
    opacity: 0;
    padding-top: 80px;
    transition: all 0.5s ease;
    background-color: var(--bg-primary);
  }

  .nav-menu.active {
    left: 0;
    opacity: 1;
    transition: all 0.5s ease;
    z-index: 1;
  }

  .nav-item {
    height: 60px;
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .nav-link {
    text-align: center;
    padding: 1rem;
    width: 100%;
    display: table;
  }

  .nav-link.active::after {
    bottom: 15px;
  }

  .menu-icon {
    display: flex;
  }

  .theme-toggle-item {
    margin: 1rem 0;
  }
}
