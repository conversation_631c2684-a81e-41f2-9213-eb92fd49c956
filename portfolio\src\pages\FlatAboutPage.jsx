import React from 'react';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import Button from '../components/Button';
import ScrollAnimation from '../components/ScrollAnimation';
import { useTheme } from '../context/ThemeContext';
import './FlatAboutPage.css';

function FlatAboutPage() {
  const { theme } = useTheme();

  return (
    <div className={`about-page ${theme}`}>
      <Navbar />

      {/* Page Header */}
      <header className="page-header">
        <div className="page-header-content">
          <ScrollAnimation animation="fade-up">
            <div className="profile-image-container">
              {/* Replace with your image */}
              <img src="https://via.placeholder.com/300" alt="John Doe" className="profile-image" />
            </div>
          </ScrollAnimation>
          <ScrollAnimation animation="fade-up" delay={0.2}>
            <h1>About Me</h1>
            <p>Learn more about my background, experience, and what drives me as a developer.</p>
          </ScrollAnimation>
        </div>
      </header>

      {/* About Section */}
      <section className="about-section">
        <div className="section-container">
          <div className="about-content">
            <ScrollAnimation animation="fade-up">
              <div className="about-text">
                <h2>Who I Am</h2>
                <p>
                  Hello! I'm a passionate software engineer currently pursuing my Bachelor of Technology at Indus University. I enjoy creating things that live on the internet, whether that be websites, applications, or anything in between. My goal is to always build products that provide pixel-perfect, performant experiences.
                </p>
                <p>
                  I'm currently working as a software engineer at FabAf, where I started as an intern. I've been focusing on frontend development, learning and applying technologies like HTML, CSS, JavaScript, and React to build responsive and user-friendly web applications.
                </p>
                <p>
                  When I'm not coding, you'll find me exploring new technologies, participating in coding challenges, or working on personal projects. I believe that continuous learning and hands-on practice are essential for growth in the ever-evolving field of web development.
                </p>

                <div className="about-buttons">
                  <Button href="/resume.pdf" type="primary">
                    <i className="fas fa-download"></i> Download Resume
                  </Button>
                  <Button to="/contact" type="secondary">
                    Contact Me
                  </Button>
                </div>
              </div>
            </ScrollAnimation>
          </div>
        </div>
      </section>

      {/* Experience Section */}
      <section className="experience-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="section-header">
              <h2 className="section-title">My Experience</h2>
              <div className="section-divider"></div>
            </div>
          </ScrollAnimation>

          <div className="timeline">
            {/* Experience Item 1 */}
            <ScrollAnimation animation="fade-up" delay={0.1}>
              <div className="timeline-item">
                <div className="timeline-dot"></div>
                <div className="timeline-date">2025 - Present</div>
                <div className="timeline-content">
                  <h3>Software Engineer</h3>
                  <h4>FabAf</h4>
                  <p>
                    Started as an intern and continued learning and growing as a software engineer. Focused on frontend development and building responsive web applications.
                  </p>
                  <ul>
                    <li>Learned HTML, CSS, JavaScript, and React</li>
                    <li>Continuing to expand knowledge and skills</li>
                    <li>Applied web development best practices in real-world projects</li>
                  </ul>
                </div>
              </div>
            </ScrollAnimation>

            {/* Experience Item 2 */}
            <ScrollAnimation animation="fade-up" delay={0.2}>
              <div className="timeline-item">
                <div className="timeline-dot"></div>
                <div className="timeline-date">2023 - 2025</div>
                <div className="timeline-content">
                  <h3>Web Development Intern</h3>
                  <h4>FabAf</h4>
                  <p>
                    Worked as an intern while completing university studies. Gained hands-on experience with modern web technologies and development practices.
                  </p>
                  <ul>
                    <li>Learned fundamentals of web development with HTML, CSS, and JavaScript</li>
                    <li>Assisted in building responsive user interfaces</li>
                    <li>Collaborated with senior developers on various projects</li>
                  </ul>
                </div>
              </div>
            </ScrollAnimation>

            {/* Experience Item 3 */}
            <ScrollAnimation animation="fade-up" delay={0.3}>
              <div className="timeline-item">
                <div className="timeline-dot"></div>
                <div className="timeline-date">2022 - 2023</div>
                <div className="timeline-content">
                  <h3>Self-Learning</h3>
                  <h4>Personal Projects</h4>
                  <p>
                    Dedicated time to self-learning and building personal projects to develop programming skills before starting internship.
                  </p>
                  <ul>
                    <li>Completed online courses in web development fundamentals</li>
                    <li>Built small projects to practice coding skills</li>
                    <li>Participated in coding challenges and hackathons</li>
                  </ul>
                </div>
              </div>
            </ScrollAnimation>
          </div>
        </div>
      </section>

      {/* Education Section */}
      <section className="education-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="section-header">
              <h2 className="section-title">Education</h2>
              <div className="section-divider"></div>
            </div>
          </ScrollAnimation>

          <div className="education-cards">
            {/* Education Card 1 */}
            <ScrollAnimation animation="fade-up" delay={0.1}>
              <div className="education-card">
                <div className="education-icon">
                  <i className="fas fa-graduation-cap"></i>
                </div>
                <h3>Bachelor of Technology</h3>
                <h4>Indus University</h4>
                <p className="education-date">2021 - 2025</p>
                <p>
                  Graduating in 2025. Focusing on computer science and software development. Participating in various tech events and coding competitions to enhance skills.
                </p>
              </div>
            </ScrollAnimation>

            {/* Education Card 2 */}
            <ScrollAnimation animation="fade-up" delay={0.2}>
              <div className="education-card">
                <div className="education-icon">
                  <i className="fas fa-certificate"></i>
                </div>
                <h3>Web Development Certification</h3>
                <h4>Online Learning Platform</h4>
                <p className="education-date">2022 - 2023</p>
                <p>
                  Completed comprehensive online courses in web development, focusing on frontend technologies including HTML, CSS, JavaScript, and React.
                </p>
              </div>
            </ScrollAnimation>
          </div>
        </div>
      </section>

      {/* Contact CTA Section */}
      <section className="contact-cta-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="contact-cta-content">
              <h2>Interested in Working Together?</h2>
              <p>Feel free to reach out if you're looking for a developer, have a question, or just want to connect.</p>
              <Button to="/contact" type="primary" size="large">
                Get In Touch
              </Button>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      <Footer />
    </div>
  );
}

export default FlatAboutPage;
