import React from 'react';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import Button from '../components/Button';
import ScrollAnimation from '../components/ScrollAnimation';
import { useTheme } from '../context/ThemeContext';
import './FlatAboutPage.css';
import image from '../assets/image.png';

function FlatAboutPage() {
  const { theme } = useTheme();

  return (
    <div className={`about-page ${theme}`}>
      <Navbar />

      {/* Page Header */}
      <header className="page-header">
        <div className="page-header-content">
          <ScrollAnimation animation="fade-up">
            <div className="profile-image-container">
              {/* Replace with your image */}
              <img src={image} alt="Divy Patel" className="profile-image" />
            </div>
          </ScrollAnimation>
          <ScrollAnimation animation="fade-up" delay={0.2}>
            <h1>About Me</h1>
            <p>Learn more about my background, experience, and what drives me as a developer.</p>
          </ScrollAnimation>
        </div>
      </header>

      {/* About Section */}
      <section className="about-section">
        <div className="section-container">
          <div className="about-content">
            <ScrollAnimation animation="fade-up">
              <div className="about-text">
                <h2>Who I Am</h2>
                <p>
                  Hello! I'm Divy Patel, a passionate Computer Science student currently pursuing my Bachelor of Technology at Indus University, Ahmedabad, Gujarat. With a CGPA of 8.4 (up to 7th semester), I enjoy creating things that live on the internet, whether that be websites, applications, or anything in between. My goal is to always build products that provide pixel-perfect, performant experiences.
                </p>
                <p>
                  I'm currently working as a Software Developer Intern at Fab AF, Ahmedabad, where I started in March 2025. I've been focusing on frontend development, learning and implementing technologies like HTML, CSS, JavaScript, and React to build responsive and user-friendly web interfaces. I'm also collaborating on internal projects to gain real-world experience in software development workflows.
                </p>
                <p>
                  When I'm not coding, you'll find me playing chess, hitting the gym, or exploring new technologies. I believe that continuous learning and hands-on practice are essential for growth in the ever-evolving field of web development. I'm fluent in Gujarati, Hindi, and English, and my strengths include leadership, time management, teamwork, problem-solving, quick learning, and presentation skills.
                </p>

                <div className="about-buttons">
                  <Button href="/resume.pdf" type="primary">
                    <i className="fas fa-download"></i> Download Resume
                  </Button>
                  <Button to="/contact" type="secondary">
                    Contact Me
                  </Button>
                </div>
              </div>
            </ScrollAnimation>
          </div>
        </div>
      </section>

      {/* Experience Section */}
      <section className="experience-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="section-header">
              <h2 className="section-title">My Experience</h2>
              <div className="section-divider"></div>
            </div>
          </ScrollAnimation>

          <div className="timeline">
            {/* Experience Item 1 */}
            <ScrollAnimation animation="fade-up" delay={0.1}>
              <div className="timeline-item">
                <div className="timeline-dot"></div>
                <div className="timeline-date">March 2025 - Present</div>
                <div className="timeline-content">
                  <h3>Software Developer Intern</h3>
                  <h4>Fab AF, Ahmedabad</h4>
                  <p>
                    Working on front-end technologies including HTML, CSS, JavaScript and React to build responsive and user-friendly web interfaces.
                  </p>
                  <ul>
                    <li>Currently learning and implementing React.js to develop modern single-page applications</li>
                    <li>Collaborating on internal projects to gain real-world experience in software development workflows</li>
                    <li>Writing clean, maintainable code and debugging issues to ensure smooth functionality</li>
                  </ul>
                </div>
              </div>
            </ScrollAnimation>
          </div>
        </div>
      </section>

      {/* Education Section */}
      <section className="education-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="section-header">
              <h2 className="section-title">Education</h2>
              <div className="section-divider"></div>
            </div>
          </ScrollAnimation>

          <div className="education-cards">
            {/* Education Card 1 */}
            <ScrollAnimation animation="fade-up" delay={0.1}>
              <div className="education-card">
                <div className="education-icon">
                  <i className="fas fa-graduation-cap"></i>
                </div>
                <h3>B.tech in Computer Science</h3>
                <h4>Indus University, Ahmedabad, Gujarat</h4>
                <p className="education-date">Sep 2021 - May 2025</p>
                <p>
                  CGPA: 8.4 (up to 7th semester). Focusing on computer science fundamentals including Data Structures, Algorithms Analysis, Database Management, Data Science, Computer Networks, Operating System, and OOPS.
                </p>
              </div>
            </ScrollAnimation>

            {/* Education Card 2 */}
            <ScrollAnimation animation="fade-up" delay={0.2}>
              <div className="education-card">
                <div className="education-icon">
                  <i className="fas fa-school"></i>
                </div>
                <h3>HSC-GSEB</h3>
                <h4>S.V High School, Kadi</h4>
                <p className="education-date">2021</p>
                <p>
                  Percentage: 73%. Completed Higher Secondary Certificate from Gujarat State Education Board.
                </p>
              </div>
            </ScrollAnimation>

            {/* Education Card 3 */}
            <ScrollAnimation animation="fade-up" delay={0.3}>
              <div className="education-card">
                <div className="education-icon">
                  <i className="fas fa-school"></i>
                </div>
                <h3>SSC-GSEB</h3>
                <h4>S.V High School, Kadi</h4>
                <p className="education-date">2019</p>
                <p>
                  Percentage: 81%. Completed Secondary School Certificate from Gujarat State Education Board.
                </p>
              </div>
            </ScrollAnimation>
          </div>
        </div>
      </section>

      {/* Certifications Section */}
      <section className="certifications-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="section-header">
              <h2 className="section-title">Certifications</h2>
              <div className="section-divider"></div>
            </div>
          </ScrollAnimation>

          <div className="certifications-grid">
            {/* Certification 1 */}
            <ScrollAnimation animation="fade-up" delay={0.1}>
              <div className="certification-card">
                <div className="certification-icon">
                  <i className="fab fa-aws"></i>
                </div>
                <h3>AWS Academy Cloud Foundations</h3>
                <h4>Amazon Web Services</h4>
                <p className="certification-status">Verified Badge</p>
                <p>
                  Comprehensive foundation in cloud computing concepts, AWS core services, security, architecture, pricing, and support.
                </p>
              </div>
            </ScrollAnimation>

            {/* Certification 2 */}
            <ScrollAnimation animation="fade-up" delay={0.2}>
              <div className="certification-card">
                <div className="certification-icon">
                  <i className="fas fa-chart-bar"></i>
                </div>
                <h3>Data Analytics</h3>
                <h4>Udemy</h4>
                <p className="certification-status">Certificate</p>
                <p>
                  Comprehensive course covering Excel, SQL, Python, and Machine Learning for data analysis and visualization.
                </p>
              </div>
            </ScrollAnimation>

            {/* Certification 3 */}
            <ScrollAnimation animation="fade-up" delay={0.3}>
              <div className="certification-card">
                <div className="certification-icon">
                  <i className="fas fa-code"></i>
                </div>
                <h3>Web Development</h3>
                <h4>Internshala</h4>
                <p className="certification-status">Certificate</p>
                <p>
                  Complete web development course covering HTML, CSS, JavaScript, and React for building modern web applications.
                </p>
              </div>
            </ScrollAnimation>
          </div>
        </div>
      </section>

      {/* Contact CTA Section */}
      <section className="contact-cta-section">
        <div className="section-container">
          <ScrollAnimation animation="fade-up">
            <div className="contact-cta-content">
              <h2>Interested in Working Together?</h2>
              <p>Feel free to reach out if you're looking for a developer, have a question, or just want to connect.</p>
              <Button to="/contact" type="primary" size="large">
                Get In Touch
              </Button>
            </div>
          </ScrollAnimation>
        </div>
      </section>

      <Footer />
    </div>
  );
}

export default FlatAboutPage;
